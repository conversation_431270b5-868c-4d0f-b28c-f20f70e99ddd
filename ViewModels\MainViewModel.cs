using Excalibur.Services;
using Excalibur.Models;
using Excalibur.Infrastructure;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class MainViewModel : ObservableObject, IDisposable
{
    private readonly IDerivApiService _derivApiService;
    private readonly ILogger<MainViewModel> _logger;
    private readonly IUserConfigurationService _configService;

    // Constants (SOLID: Single Responsibility)
    private const decimal MinStakeAllowed = 0.35m;

    private bool _isConnected;
    public bool IsConnected
    {
        get => _isConnected;
        set { _isConnected = value; OnPropertyChanged(); }
    }

    private string _accountCode = "-----------";
    public string AccountCode
    {
        get => _accountCode;
        set { _accountCode = value; OnPropertyChanged(); }
    }
    
    private string _accountType = "---";
    public string AccountType
    {
        get => _accountType;
        set { _accountType = value; OnPropertyChanged(); }
    }

    private double _balance;
    public double Balance
    {
        get => _balance;
        set { _balance = value; OnPropertyChanged(); }
    }

    private double _initialBalance;
    public double InitialBalance
    {
        get => _initialBalance;
        set { _initialBalance = value; OnPropertyChanged(); }
    }

    private long _ping;
    public long Ping
    {
        get => _ping;
        set { _ping = value; OnPropertyChanged(); }
    }
    
    // Properties for Contract Info Card
    private string _selectedContractDisplayName = "Nenhum contrato selecionado";
    public string SelectedContractDisplayName
    {
        get => _selectedContractDisplayName;
        set { _selectedContractDisplayName = value; OnPropertyChanged(); }
    }
    
    // Simplified properties for compact display
    public string ContractSymbol => SelectedActiveSymbol?.Symbol ?? "---";
    public string ContractTypeDisplay => SelectedContractType != null ? $"{SelectedContractType.CategoryDisplay} - {SelectedContractType.ContractDisplay}" : "---";
    
    private decimal _currentTickPrice;
    
    // Propriedades para análise de volatilidade
    private readonly Queue<decimal> _priceHistory = new();
        private decimal _priceDirection = 0;
    private readonly Queue<DateTime> _priceTimestamps = new();
    private decimal _volatilityIndex = 0;
    private decimal _averagePrice = 0;
    private readonly int _maxPriceHistorySize = 50; // Últimos 50 ticks para análise
    
    public decimal VolatilityIndex
    {
        get => _volatilityIndex;
        private set { _volatilityIndex = value; OnPropertyChanged(); }
    }
    
    public decimal AveragePrice
    {
        get => _averagePrice;
        private set { _averagePrice = value; OnPropertyChanged(); }
    }
    
    // Propriedades para proteção de drawdown
    private decimal _maxHistoricalDrawdown = 0;
    private decimal _currentDrawdown = 0;
    private decimal _peakBalance = 0;
    private bool _drawdownProtectionActive = false;
    
    public decimal MaxHistoricalDrawdown
    {
        get => _maxHistoricalDrawdown;
        private set { _maxHistoricalDrawdown = value; OnPropertyChanged(); }
    }
    
    public decimal CurrentDrawdown
    {
        get => _currentDrawdown;
        private set { _currentDrawdown = value; OnPropertyChanged(); }
    }
    
    public bool DrawdownProtectionActive
    {
        get => _drawdownProtectionActive;
        private set { _drawdownProtectionActive = value; OnPropertyChanged(); }
    }
    
    // Propriedades para recuperação multi-sessão
    private readonly Queue<SessionPerformanceData> _sessionHistory = new();
    private decimal _cumulativeSessionProfit = 0;
    private int _consecutiveLossSessions = 0;
    private decimal _multiSessionRecoveryFactor = 1.0m;
    private readonly int _maxSessionHistory = 20; // Últimas 20 sessões
    
    public decimal CumulativeSessionProfit
    {
        get => _cumulativeSessionProfit;
        private set { _cumulativeSessionProfit = value; OnPropertyChanged(); }
    }
    
    public int ConsecutiveLossSessions
    {
        get => _consecutiveLossSessions;
        private set { _consecutiveLossSessions = value; OnPropertyChanged(); }
    }
    
    public decimal MultiSessionRecoveryFactor
    {
        get => _multiSessionRecoveryFactor;
        private set { _multiSessionRecoveryFactor = value; OnPropertyChanged(); }
    }
    
    // Propriedades de pausa automática removidas
    
    // Propriedades do sistema de recuperação escalonada
    private int _recoveryLevel = 0;
    private decimal _baseRecoveryMultiplier = 1.5m;
    private decimal _maxRecoveryMultiplier = 4.0m;
    private int _maxRecoveryLevel = 3;
    private decimal _recoveryThreshold = 0.7m; // 70% de taxa de vitória para sair do modo recuperação
    private bool _isRecoveryMode = false;
    private DateTime _recoveryModeStartTime;
    private decimal _recoveryModeStartBalance;
    private List<decimal> _recentWinRates = new List<decimal>();
    private int _winRateCalculationPeriod = 20; // Últimas 20 operações
    
    public int RecoveryLevel
    {
        get => _recoveryLevel;
        private set { _recoveryLevel = value; OnPropertyChanged(); }
    }
    
    public bool IsRecoveryMode
    {
        get => _isRecoveryMode;
        private set { _isRecoveryMode = value; OnPropertyChanged(); }
    }
    
    public decimal CurrentRecoveryMultiplier
    {
        get
        {
            if (!_isRecoveryMode || _recoveryLevel == 0)
                return 1.0m;
                
            var multiplier = _baseRecoveryMultiplier * (1 + (_recoveryLevel * 0.3m));
            return Math.Min(multiplier, _maxRecoveryMultiplier);
        }
    }
    
    public decimal CurrentWinRate
    {
        get
        {
            if (_recentWinRates.Count == 0) return 0m;
            return _recentWinRates.Average();
        }
    }
    
    // Propriedades do fator de confiança
    private decimal _confidenceFactor = 1.0m;
    private List<bool> _recentResults = new List<bool>();
    private int _confidenceCalculationPeriod = 15; // Últimas 15 operações
    private decimal _minConfidenceFactor = 0.5m;
    private decimal _maxConfidenceFactor = 1.3m;
    
    public decimal ConfidenceFactor
    {
        get => _confidenceFactor;
        private set { _confidenceFactor = value; OnPropertyChanged(); }
    }
    
    public decimal ConfidenceAdjustedStake
    {
        get
        {
            var baseStake = _isRecoveryMode ? Stake * CurrentRecoveryMultiplier : Stake;
            return Math.Round(baseStake * _confidenceFactor, 2);
        }
    }
    
    // Propriedades de otimização de timing
    private decimal _momentumScore = 1.0m;
    private List<decimal> _momentumHistory = new List<decimal>();
    private int _momentumCalculationPeriod = 10;
    private decimal _optimalMomentumThreshold = 0.3m; // Reduzido para 0.3m para permitir mais entradas automáticas
    private bool _isWaitingForOptimalTiming = false;
    private DateTime _lastTimingCheck = DateTime.MinValue;
    private TimeSpan _timingCheckInterval = TimeSpan.FromSeconds(2);
    
    public decimal MomentumScore
    {
        get => _momentumScore;
        private set { _momentumScore = value; OnPropertyChanged(); }
    }
    
    public bool IsOptimalTiming
    {
        get => _momentumScore >= _optimalMomentumThreshold;
    }
    
    public bool IsWaitingForOptimalTiming
    {
        get => _isWaitingForOptimalTiming;
        private set { _isWaitingForOptimalTiming = value; OnPropertyChanged(); }
    }
    
    public decimal CurrentTickPrice
    {
        get => _currentTickPrice;
        set 
        { 
            var previousPrice = _currentTickPrice;
            _currentTickPrice = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(FormattedTickPrice));
            OnPropertyChanged(nameof(SpotPriceDisplay));
            
            // Update price direction
            if (previousPrice != 0 && value != previousPrice)
            {
                IsPriceUp = value > previousPrice;
                OnPropertyChanged(nameof(IsPriceUp));
                OnPropertyChanged(nameof(SpotColor));
                OnPropertyChanged(nameof(PriceArrow));
            }
            
            // Atualizar análise de volatilidade
            UpdateVolatilityAnalysis(value);
        }
    }
    
    public string FormattedTickPrice
    {
        get => _currentTickPrice == 0 ? "---" : _currentTickPrice.ToString("F5");
    }
    
    // Price direction and formatting properties
    public bool IsPriceUp { get; private set; }
    
    public string SpotPriceDisplay
    {
        get
        {
            if (_currentTickPrice == 0) return "---";
            // Always show exactly 3 decimal places
            return _currentTickPrice.ToString("F3");
        }
    }
    
    public string SpotColor
    {
        get => _currentTickPrice == 0 ? "White" : (IsPriceUp ? "#FF2ECC71" : "#FFFF4444");
    }
    
    public string PriceArrow
    {
        get => _currentTickPrice == 0 ? "" : (IsPriceUp ? "▲" : "▼");
    }
    
    private DateTime _lastTickTime;
    public DateTime LastTickTime
    {
        get => _lastTickTime;
        set { _lastTickTime = value; OnPropertyChanged(); }
    }

    // Coleções para seleção de contratos
    private ObservableCollection<string> _markets = [];
    public ObservableCollection<string> Markets
    {
        get => _markets;
        set { _markets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<string> _subMarkets = [];
    public ObservableCollection<string> SubMarkets
    {
        get => _subMarkets;
        set { _subMarkets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ActiveSymbol> _activeSymbols = [];
    public ObservableCollection<ActiveSymbol> ActiveSymbols
    {
        get => _activeSymbols;
        set { _activeSymbols = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ContractDetails> _contractTypes = [];
    public ObservableCollection<ContractDetails> ContractTypes
    {
        get => _contractTypes;
        set 
        { 
            _contractTypes = value; 
            OnPropertyChanged(); 
            
            // Aplicar seleções pendentes quando os tipos de contrato são carregados
            if (value?.Any() == true)
            {
                ApplyPendingSelections();
            }
        }
    }

    private ObservableCollection<ProfitTableEntry> _profitTableEntries = [];
    public ObservableCollection<ProfitTableEntry> ProfitTableEntries
    {
        get => _profitTableEntries;
        set { _profitTableEntries = value; OnPropertyChanged(); }
    }

    // Propriedades para seleções atuais
    private string? _selectedMarket;
    public string? SelectedMarket
    {
        get => _selectedMarket;
        set
        {
            _selectedMarket = value;
            OnPropertyChanged();
            
            // Auto-save selection to pending configuration
            _pendingMarketSelection = value;
            _ = Task.Run(async () => await SaveConfigurationAsync());
            
            OnMarketSelectionChanged();
        }
    }

    private decimal CalculateNextMartingaleStake()
    {
        // Use TryGetStakeAmountDecimal to avoid culture-dependent parsing issues
        if (InitialStakeAmount == 0)
        {
            // A stake mínima inicial deve ser igual ao campo Stake
            InitialStakeAmount = Stake > 0 ? Stake : (TryGetStakeAmountDecimal(out decimal initialStake) ? initialStake : MinStakeAllowed);
        }

        var nextLevel = CurrentMartingaleLevel + 1;
        if (nextLevel == 1)
        {
            return InitialStakeAmount;
        }
        else
        {
            return InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, nextLevel - 1);
    }
    }

    private string? _selectedSubMarket;
    public string? SelectedSubMarket
    {
        get => _selectedSubMarket;
        set
        {
            _selectedSubMarket = value;
            OnPropertyChanged();
            
            // Auto-save selection to pending configuration
            _pendingSubMarketSelection = value;
            _ = Task.Run(async () => await SaveConfigurationAsync());
            
            OnSubMarketSelectionChanged();
        }
    }

    private ActiveSymbol? _selectedActiveSymbol;
    public ActiveSymbol? SelectedActiveSymbol
    {
        get => _selectedActiveSymbol;
        set
        {
            _selectedActiveSymbol = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ContractSymbol));
            
            // Auto-save selection to pending configuration
            _pendingActiveSymbolSelection = value?.Symbol;
            _ = Task.Run(async () => await SaveConfigurationAsync());
            
            // Update chart title and clear previous data only if NOT restoring after reconnection
            if (ChartViewModel != null)
            {
                if (value != null)
                {
                    ChartViewModel.UpdateChartTitle(value.Symbol);
                    // Only clear data if this is a user-initiated change, not a restoration
                    if (!_isRestoringSelections)
                    {
                        ChartViewModel.ClearData(); // Clear previous symbol's data
                    }
                }
                else
                {
                    ChartViewModel.UpdateChartTitle(string.Empty);
                    if (!_isRestoringSelections)
                    {
                        ChartViewModel.ClearData();
                    }
                }
            }
            
            OnActiveSymbolSelectionChanged();
        }
    }

    private ContractDetails? _selectedContractType;
    public ContractDetails? SelectedContractType
    {
        get => _selectedContractType;
        set
        {
            _selectedContractType = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ContractTypeDisplay));
            
            // Auto-save selection to pending configuration
            _pendingContractTypeSelection = value?.ContractType;
            
            UpdateContractParameters(); // Método chave para atualizar a UI
            
            // Auto-save configuration when contract type changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
            
            // Update contract display name and subscribe to ticks
            if (value != null && SelectedActiveSymbol != null)
            {
                // Subscribe to ticks for the selected symbol
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _derivApiService.SubscribeToTicksAsync(SelectedActiveSymbol.Symbol);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to subscribe to ticks for selected symbol");
                    }
                });
            }
            else
            {
                _derivApiService.UnsubscribeFromTicks();
            }
        }
    }

    // Novas propriedades para controlar a visibilidade da UI
    private bool _isDurationVisible;
    public bool IsDurationVisible { get => _isDurationVisible; set { _isDurationVisible = value; OnPropertyChanged(); } }

    private bool _isBarrier1Visible;
    public bool IsBarrier1Visible { get => _isBarrier1Visible; set { _isBarrier1Visible = value; OnPropertyChanged(); } }

    private bool _isBarrier2Visible;
    public bool IsBarrier2Visible { get => _isBarrier2Visible; set { _isBarrier2Visible = value; OnPropertyChanged(); } }

    private bool _isDigitSelectionVisible;
    public bool IsDigitSelectionVisible { get => _isDigitSelectionVisible; set { _isDigitSelectionVisible = value; OnPropertyChanged(); } }

    private string _durationInfo = string.Empty;
    public string DurationInfo { get => _durationInfo; set { _durationInfo = value; OnPropertyChanged(); } }

    // Propriedades para Stake e cálculo de payout
    private decimal _stake = 0.35m;
    public decimal Stake 
    { 
        get => _stake; 
        set
        {
            // Apply minimum stake validation only for external callers, not during text editing
            _stake = Math.Round(Math.Max(value, MinStakeAllowed), 2);
            OnPropertyChanged();
            CalculateProposalAsync();

            // CORREÇÃO: Atualizar MaxStake quando stake atual muda
            if (_stake > MaxStake)
            {
                decimal oldMaxStake = MaxStake;
                MaxStake = _stake;
                Console.WriteLine($"[MAX STAKE] Updated from current stake: {oldMaxStake:F2} -> {MaxStake:F2}");
                OnPropertyChanged(nameof(MaxStake));
            }

            // Auto-save configuration when stake changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    // Internal setter for StakeAmount that bypasses minimum validation during editing
    private void SetStakeRaw(decimal value)
    {
        _stake = Math.Round(value, 2);
        OnPropertyChanged(nameof(Stake));
    }

    // StakeAmount property moved to end of class to integrate with Martingale

    private string _barrier1Value = string.Empty;
    public string Barrier1Value 
    { 
        get => _barrier1Value; 
        set 
        { 
            _barrier1Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _barrier2Value = string.Empty;
    public string Barrier2Value 
    { 
        get => _barrier2Value; 
        set 
        { 
            _barrier2Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _durationValue = 5;
    public int DurationValue 
    { 
        get => _durationValue; 
        set 
        { 
            _durationValue = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _durationUnit = "t";
    public string DurationUnit 
    { 
        get => _durationUnit; 
        set 
        { 
            _durationUnit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _selectedDigit = 0;
    public int SelectedDigit 
    { 
        get => _selectedDigit; 
        set 
        { 
            _selectedDigit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // Trading Control Properties (SOLID: Single Responsibility)
    private bool _isTradingEnabled = true;
    public bool IsTradingEnabled 
    { 
        get => _isTradingEnabled; 
        set 
        { 
            _isTradingEnabled = value; 
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanExecuteBuy));
            _logger.LogInformation("Trading {Status} by user", value ? "ENABLED" : "DISABLED");
        } 
    }

    // Propriedades para exibir resultados do cálculo
    private decimal _calculatedPayout;
    public decimal CalculatedPayout { get => _calculatedPayout; set { _calculatedPayout = value; OnPropertyChanged(); } }

    private decimal _askPrice;
    public decimal AskPrice
    {
        get => _askPrice;
        set { _askPrice = value; OnPropertyChanged(); }
    }

    private string? _currentProposalId;
    public string? CurrentProposalId
    {
        get => _currentProposalId;
        set { _currentProposalId = value; OnPropertyChanged(); }
    }

    private string _calculatedBarrier1 = string.Empty;
    public string CalculatedBarrier1 { get => _calculatedBarrier1; set { _calculatedBarrier1 = value; OnPropertyChanged(); } }

    private string _calculatedBarrier2 = string.Empty;
    public string CalculatedBarrier2 { get => _calculatedBarrier2; set { _calculatedBarrier2 = value; OnPropertyChanged(); } }

    private string _barrier1Suggestion = string.Empty;
    public string Barrier1Suggestion { get => _barrier1Suggestion; set { _barrier1Suggestion = value; OnPropertyChanged(); } }

    private string _barrier2Suggestion = string.Empty;
    public string Barrier2Suggestion { get => _barrier2Suggestion; set { _barrier2Suggestion = value; OnPropertyChanged(); } }

    private bool _isCalculating;
    public bool IsCalculating { get => _isCalculating; set { _isCalculating = value; OnPropertyChanged(); } }

    // Propriedades do Martingale
    private bool _isMartingaleEnabled;
    public bool IsMartingaleEnabled 
    { 
        get => _isMartingaleEnabled; 
        set 
        { 
            _isMartingaleEnabled = value; 
            OnPropertyChanged();
            if (value)
            {
                IsNoneSelected = false;
                IsDualEnabled = false;
            }
            else
            {
                // Reset martingale state when disabled
                CurrentMartingaleLevel = 0;
                NextStakeAmount = TryGetStakeAmountDecimal(out decimal currentStake) ? currentStake : 0;
            }
        } 
    }

    private decimal _martingaleFactor = 2.0m;
    public decimal MartingaleFactor
    {
        get => _martingaleFactor;
        set
        {
            // Arredonda para duas casas decimais
            _martingaleFactor = Math.Round(value, 2);
            OnPropertyChanged();
            CalculateNextStake();

            // Auto-save configuration when martingale factor changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    private int _martingaleLevel = 3;
    public int MartingaleLevel
    {
        get => _martingaleLevel;
        set
        {
            _martingaleLevel = value;
            OnPropertyChanged();

            // Auto-save configuration when martingale level changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    private bool _isFastMartingale;
    public bool IsFastMartingale 
    { 
        get => _isFastMartingale; 
        set 
        { 
            _isFastMartingale = value; 
            OnPropertyChanged();
            
            if (value && IsMartingaleEnabled)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population");
                
                // IMMEDIATE AGGRESSIVE POPULATION: Ensure pool is ready instantly
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await PopulateHotProposalPoolImmediate();
                        
                        lock (_poolLock)
                        {
                            var readyLevels = _hotProposalPool.Count;
                            _logger.LogInformation("[DEBUG] Fast Martingale READY: {ReadyLevels} proposals pre-calculated and ready for instant execution", readyLevels);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] Fast Martingale enable: Failed to populate hot pool");
                    }
                });
            }
            else if (!value)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale DISABLED - clearing hot pool");
                
                // Clear pool when disabled to save memory
                lock (_poolLock)
                {
                    _hotProposalPool.Clear();
                }
            }
        } 
    }

    // HOT PROPOSAL POOL - Pre-calculated proposals ready for instant execution
    private readonly Dictionary<int, ProposalResponse> _hotProposalPool = [];
    private readonly object _poolLock = new();
    private bool _isPoolPopulating = false;


    private decimal _nextStakeAmount;
    public decimal NextStakeAmount 
    { 
        get => _nextStakeAmount; 
        set 
        { 
            _nextStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private decimal _initialStakeAmount;

    // Propriedade para o radiobutton "Nenhum"
    private bool _isNoneSelected = true;
    public bool IsNoneSelected 
    { 
        get => _isNoneSelected; 
        set 
        { 
            _isNoneSelected = value; 
            OnPropertyChanged();
            if (value)
            {
                IsMartingaleEnabled = false;
                IsDualEnabled = false;
            }
        } 
    }
    public decimal InitialStakeAmount
    {
        get => _initialStakeAmount;
        set
        {
            _initialStakeAmount = value;
            OnPropertyChanged();

            // Auto-save configuration when initial stake amount changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    private decimal _maxLossAmount;
    public decimal MaxLossAmount
    {
        get => _maxLossAmount;
        set
        {
            _maxLossAmount = value;
            OnPropertyChanged();

            // Auto-save configuration when max loss amount changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    private int _currentMartingaleLevel;
    public int CurrentMartingaleLevel 
    { 
        get => _currentMartingaleLevel; 
        set 
        { 
            _currentMartingaleLevel = value; 
            OnPropertyChanged();
            CalculateNextStake();
            // MaxLevel deve refletir o nível atual (CurrentMartingaleLevel)
            // Só atualiza MaxLevel se o nível atual for maior que o MaxLevel atual
            // E não deve ultrapassar o limite definido em MartingaleLevel
            int currentLevel = value; // Nível exibido na interface (começando do 0)
            if (currentLevel > MaxLevel && currentLevel <= MartingaleLevel) 
                MaxLevel = currentLevel;
        } 
    }

    // Propriedades do Modo Dual
    private bool _isDualEnabled;
    public bool IsDualEnabled
    {
        get => _isDualEnabled;
        set
        {
            _isDualEnabled = value;
            OnPropertyChanged();
            if (value)
            {
                IsNoneSelected = false;
                IsMartingaleEnabled = false;
                PopulateDualContractTypes();

                _logger.LogInformation($"[DUAL_INIT] Modo dual habilitado - SessionProfit: {SessionProfit:F2}, TotalProfit: {TotalProfit:F2}");
            }
        }
    }

    // Propriedade para controlar modo automático vs manual
    private bool _isDualAutoMode = false;
    public bool IsDualAutoMode
    {
        get => _isDualAutoMode;
        set
        {
            _isDualAutoMode = value;
            OnPropertyChanged();
            _logger.LogInformation($"[DUAL MODE] Modo alterado para: {(value ? "AUTOMÁTICO" : "MANUAL")}");

            // Auto-save configuration when dual auto mode changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }





    // Propriedades do Novo Modo Dual conforme especificações
    private decimal _dualLucroAlvo = 1.00m;
    public decimal DualLucroAlvo
    {
        get => _dualLucroAlvo;
        set
        {
            _dualLucroAlvo = Math.Round(value, 2);
            OnPropertyChanged();

            // Auto-save configuration when lucro alvo changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    // α - fração do prejuízo a recuperar em cada parcela
    private decimal _dualAlfa = 0.35m;
    public decimal DualAlfa
    {
        get => _dualAlfa;
        set
        {
            _dualAlfa = Math.Round(value, 2);
            OnPropertyChanged();

            // Auto-save configuration when alfa changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    // Lucro mínimo por parcela
    private decimal _dualLucroBase = 0.04m;
    public decimal DualLucroBase
    {
        get => _dualLucroBase;
        set
        {
            _dualLucroBase = Math.Round(value, 2);
            OnPropertyChanged();

            // Auto-save configuration when lucro base changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    // No novo esquema, este campo representa a razão R = y/x
    private decimal _dualP = 1.90m;
    public decimal DualP
    {
        get => _dualP;
        set
        {
            _dualP = Math.Round(value, 2);
            OnPropertyChanged();

            // Auto-save configuration when P changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    private decimal _dualMaxLossAmount = 10.00m;
    public decimal DualMaxLossAmount
    {
        get => _dualMaxLossAmount;
        set
        {
            _dualMaxLossAmount = Math.Round(value, 2);
            OnPropertyChanged();

            // Auto-save configuration when dual max loss amount changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    // Manter compatibilidade com código existente
    private decimal _dualTakeProfit = 5.00m;
    public decimal DualTakeProfit
    {
        get => _dualTakeProfit;
        set
        {
            _dualTakeProfit = Math.Round(value, 2);
            OnPropertyChanged();

            // Auto-save configuration when take profit changes
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }





    // Método removido - funcionalidade de inversão foi descontinuada



    private int _dualLevel = 5;
    public int DualLevel 
    { 
        get => _dualLevel; 
        set 
        { 
            _dualLevel = value; 
            OnPropertyChanged();
        } 
    }

    private int _dualSession = 5; // Valor padrão maior para permitir múltiplas sessões
    public int DualSession 
    { 
        get => _dualSession; 
        set 
        { 
            _dualSession = value; 
            OnPropertyChanged();
        } 
    }

    private ObservableCollection<ContractDetails> _dualContractTypes = [];
    public ObservableCollection<ContractDetails> DualContractTypes
    {
        get => _dualContractTypes;
        set { _dualContractTypes = value; OnPropertyChanged(); }
    }

    private ContractDetails? _selectedDualContractType;
    public ContractDetails? SelectedDualContractType
    {
        get => _selectedDualContractType;
        set
        {
            _selectedDualContractType = value;
            OnPropertyChanged();
            _pendingDualContractTypeSelection = value?.ContractType;
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    // Variável para rastrear perdas acumuladas no novo modo dual
    private decimal _dualPerdasAcumuladas = 0m;
    public decimal DualPerdasAcumuladas
    {
        get => _dualPerdasAcumuladas;
        private set
        {
            _dualPerdasAcumuladas = Math.Round(value, 2);
            OnPropertyChanged();
        }
    }

    // Controle de Sessões Dual
    private int _currentDualLevel;
    public int CurrentDualLevel 
    { 
        get => _currentDualLevel; 
        set 
        { 
            _currentDualLevel = value; 
            OnPropertyChanged();
            // MaxLevel deve refletir o nível atual (CurrentDualLevel)
            // Só atualiza MaxLevel se o nível atual for maior que o MaxLevel atual
            // E não deve ultrapassar o limite definido em DualLevel
            int currentLevel = value; // Nível exibido na interface (começando do 0)
            if (currentLevel > MaxLevel && currentLevel <= DualLevel) 
                MaxLevel = currentLevel;
        } 
    }

    private int _currentDualSession = 0;
    public int CurrentDualSession 
    { 
        get => _currentDualSession; 
        set 
        { 
            _currentDualSession = value; 
            OnPropertyChanged();
        } 
    }

    private decimal _sessionProfit;
    private decimal _previousSessionProfit = 0m; // Para rastrear variação do SessionProfit
    
    public decimal SessionProfit
    {
        get => _sessionProfit;
        set
        {
            _previousSessionProfit = _sessionProfit; // Armazenar valor anterior
            _sessionProfit = Math.Round(value, 2);
            OnPropertyChanged();
            // CORREÇÃO: Notificar TotalProfit quando SessionProfit muda (especialmente importante no modo dual)
            OnPropertyChanged(nameof(TotalProfit));
        }
    }

    // CORREÇÃO: Rastrear lucros apenas de sessões completadas
    private decimal _completedSessionsProfit = 0m;
    
    public decimal TotalProfit 
    { 
        // No modo dual com micro-metas, TotalProfit é apenas o acumulado transferido
        get => IsDualEnabled ? Math.Round(_completedSessionsProfit, 2)
                              : Math.Round(_completedSessionsProfit + SessionProfit, 2);
        set 
        { 
            decimal oldValue = _completedSessionsProfit;
            _completedSessionsProfit = Math.Round(value, 2); 
            OnPropertyChanged();
            
            // Debug: Track balance discrepancy and profit changes usando método centralizado
            double expectedBalance = CalculateExpectedBalance();
            double balanceDiscrepancy = expectedBalance - Balance;
            
            Console.WriteLine($"[TOTAL PROFIT] CompletedSessions changed: {oldValue:F2} -> {_completedSessionsProfit:F2} (diff: {(_completedSessionsProfit - oldValue):F2})");
            Console.WriteLine($"[TOTAL PROFIT] Total displayed: {TotalProfit:F2} (Completed: {_completedSessionsProfit:F2} + Session: {SessionProfit:F2})");
            Console.WriteLine($"[BALANCE DEBUG] Expected Balance: {InitialBalance:F2} + {TotalProfit:F2} - Active({ActiveExposure:F2}) = {expectedBalance:F2}");
            Console.WriteLine($"[BALANCE DEBUG] API Balance: {Balance:F2}");
            Console.WriteLine($"[BALANCE DEBUG] Discrepancy: {balanceDiscrepancy:F2}");
            
            // Immediate verification when TotalProfit changes
            if (ProfitTableEntries?.Count > 0)
            {
                decimal tableSum = ProfitTableEntries.Sum(entry => entry.TotalProfitLoss);
                decimal difference = TotalProfit - tableSum;
                Console.WriteLine($"[PROFIT CHECK] Total Profit: {TotalProfit:F2}, Table Sum: {tableSum:F2}, Diff: {difference:F2}");
                
                // Durante sessão ativa, diferença é esperada (tabela contém operações atuais)
                if (IsDualEnabled && CurrentDualSession > 0)
                {
                    Console.WriteLine($"[PROFIT CHECK] Session active - difference expected (current session operations in table)");
                }
                else if (Math.Abs(difference) > 1.0m)
                {
                    Console.WriteLine($"[PROFIT ERROR] Large discrepancy detected in TotalProfit calculation!");
                }
            }
        }
    }

    private decimal _maxStake;
    public decimal MaxStake 
    { 
        get => _maxStake; 
        set 
        { 
            _maxStake = Math.Round(value, 2); 
            OnPropertyChanged(); 
        }
    }

    // Max Level alcançado (dual ou martingale)
    private int _maxLevel;
    public int MaxLevel
    {
        get => _maxLevel;
        set { _maxLevel = value; OnPropertyChanged(); }
    }

    // Exposição ativa (stakes em contratos ainda abertos)
    public decimal ActiveExposure 
    {
        get
        {
            var activeEntries = ProfitTableEntries.Where(e => e.IsActive).ToList();
            var totalExposure = activeEntries.Sum(e => e.Stake);
            
            _logger.LogDebug($"[ACTIVE_EXPOSURE] Calculando exposição ativa:");
            _logger.LogDebug($"[ACTIVE_EXPOSURE] Total de contratos ativos: {activeEntries.Count}");
            
            foreach (var entry in activeEntries)
            {
                _logger.LogDebug($"[ACTIVE_EXPOSURE] Contrato {entry.RefId}: Stake={entry.Stake:F2}, Tipo={entry.Contract}");
            }
            
            _logger.LogDebug($"[ACTIVE_EXPOSURE] Exposição total calculada: {totalExposure:F2}");
            
            return totalExposure;
        }
    }

    // Calculated property to show what balance should be durante sessão ativa
    public double CalculatedBalance => CalculateExpectedBalance();
    
    /// <summary>
    /// Método centralizado para calcular o saldo esperado, eliminando duplicações
    /// </summary>
    private double CalculateExpectedBalance()
    {
        // CORREÇÃO CRÍTICA: Usar apenas decimal para evitar problemas de precisão
        decimal initialBalance = Math.Round((decimal)InitialBalance, 2);
        decimal totalProfit = Math.Round(TotalProfit, 2);
        decimal activeExposure = Math.Round(ActiveExposure, 2);
        
        // Cálculo usando apenas decimal para máxima precisão
        decimal expectedBalanceDecimal = initialBalance + totalProfit - activeExposure;
        expectedBalanceDecimal = Math.Round(expectedBalanceDecimal, 2);
        
        // Converter para double apenas no retorno
        double expectedBalance = (double)expectedBalanceDecimal;
        
        // Log detalhado para debugging
        _logger.LogDebug("[BALANCE_CALC] Expected Balance Calculation (FIXED):");
        _logger.LogDebug("[BALANCE_CALC] - InitialBalance: {InitialBalance:F2}", initialBalance);
        _logger.LogDebug("[BALANCE_CALC] - TotalProfit: {TotalProfit:F2} (Completed: {CompletedProfit:F2} + Session: {SessionProfit:F2})", totalProfit, _completedSessionsProfit, SessionProfit);
        _logger.LogDebug("[BALANCE_CALC] - ActiveExposure: {ActiveExposure:F2}", activeExposure);
        _logger.LogDebug("[BALANCE_CALC] - Expected Result (decimal): {ExpectedBalanceDecimal:F2}", expectedBalanceDecimal);
        _logger.LogDebug("[BALANCE_CALC] - Expected Result (double): {ExpectedBalance:F2}", expectedBalance);
        
        return expectedBalance;
    }
    
    /// <summary>
    /// Verifica a consistência entre o saldo calculado e o saldo da API
    /// </summary>
    private void ValidateBalanceConsistency(double apiBalance, string context = "")
    {
        var expectedBalance = CalculateExpectedBalance();
        var discrepancy = Math.Abs(expectedBalance - apiBalance);
        var discrepancyPercentage = apiBalance > 0 ? (discrepancy / apiBalance) * 100 : 0;
        
        _logger.LogInformation($"[BALANCE_VALIDATION] {context}");
        _logger.LogInformation($"[BALANCE_VALIDATION] - API Balance: {apiBalance:F2}");
        _logger.LogInformation($"[BALANCE_VALIDATION] - Expected Balance: {expectedBalance:F2}");
        _logger.LogInformation($"[BALANCE_VALIDATION] - Discrepancy: {discrepancy:F2} ({discrepancyPercentage:F2}%)");
        _logger.LogInformation($"[BALANCE_VALIDATION] - Initial Balance: {InitialBalance:F2}");
        _logger.LogInformation($"[BALANCE_VALIDATION] - Completed Sessions Profit: {_completedSessionsProfit:F2}");
        _logger.LogInformation($"[BALANCE_VALIDATION] - Session Profit: {SessionProfit:F2}");
        _logger.LogInformation($"[BALANCE_VALIDATION] - Active Exposure: {ActiveExposure:F2}");
        
        // Alerta se discrepância for significativa (mais de 1% ou mais de $1)
        if (discrepancy > 1.0 || discrepancyPercentage > 1.0)
        {
            _logger.LogWarning($"[BALANCE_ALERT] Discrepância significativa detectada!");
            _logger.LogWarning($"[BALANCE_ALERT] Diferença: {discrepancy:F2} ({discrepancyPercentage:F2}%)");
            _logger.LogWarning($"[BALANCE_ALERT] Contexto: {context}");
            
            // Se a discrepância for muito grande (>5%), pode indicar problema sério
            if (discrepancyPercentage > 5.0)
            {
                _logger.LogError($"[BALANCE_ERROR] Discrepância crítica detectada: {discrepancyPercentage:F2}%");
                _logger.LogError($"[BALANCE_ERROR] Possível problema na contabilização ou sincronização com API");
            }
        }
        else
        {
            _logger.LogInformation($"[BALANCE_VALIDATION] Saldo consistente - diferença aceitável");
        }
    }

    // Method to force recalculate TotalProfit from ProfitTable
    public void RecalculateTotalProfitFromTable()
    {
        // CORREÇÃO: CompletedSessionsProfit NÃO deve ser recalculado da tabela
        // Ele é gerenciado exclusivamente pelos métodos CompleteSession() e StartNewSession()
        decimal calculatedTotal = ProfitTableEntries.Sum(entry => entry.TotalProfitLoss);
        decimal currentCompleted = _completedSessionsProfit;
        decimal currentSession = SessionProfit;
        
        _logger.LogDebug($"[RECALC] Table total: {calculatedTotal:F2}, CompletedSessionsProfit: {currentCompleted:F2}, SessionProfit: {currentSession:F2}");
        
        // CORREÇÃO: Verificar se há discrepância na tabela vs valores controlados
        decimal expectedTableTotal = currentCompleted + currentSession;
        decimal discrepancy = Math.Abs(calculatedTotal - expectedTableTotal);
        
        if (discrepancy > 0.01m) // Tolerância de 1 centavo para arredondamento
        {
            _logger.LogWarning($"[RECALC] Discrepância detectada na tabela: {discrepancy:F2}");
            _logger.LogWarning($"[RECALC] Tabela: {calculatedTotal:F2}, Esperado: {expectedTableTotal:F2}");
            _logger.LogWarning($"[RECALC] CompletedSessionsProfit: {currentCompleted:F2}, SessionProfit: {currentSession:F2}");
            
            // CORREÇÃO: Não alterar CompletedSessionsProfit - ele é autoritativo
            // A tabela pode ter inconsistências temporárias durante operações
            _logger.LogInformation($"[RECALC] Mantendo CompletedSessionsProfit autoritativo: {currentCompleted:F2}");
        }
        else
        {
            _logger.LogDebug($"[RECALC] Tabela consistente com valores controlados");
        }
    }

    // Method to verify TotalProfit calculation from profit table
    public decimal CalculatedTotalProfitFromTable()
    {
        // Esta função calcula o total da tabela (incluindo sessão atual)
        // Diferente de CompletedSessionsProfit que só inclui sessões finalizadas
        return ProfitTableEntries.Sum(entry => entry.TotalProfitLoss);
    }

    // Method to update TotalProfit from table for all operations
    public void UpdateTotalProfitFromTable()
    {
        decimal calculatedTotal = ProfitTableEntries.Sum(entry => entry.TotalProfitLoss);
        decimal previousTotal = TotalProfit;
        decimal previousSessionProfit = SessionProfit;
        decimal previousCompletedSessions = _completedSessionsProfit;

        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] ===== INÍCIO UpdateTotalProfitFromTable =====");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Calculated table sum: {calculatedTotal:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Previous TotalProfit: {previousTotal:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Previous SessionProfit: {previousSessionProfit:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Previous CompletedSessions: {previousCompletedSessions:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] IsDualEnabled: {IsDualEnabled}");

        if (!IsDualEnabled)
        {
            // Para operações não-dual, TotalProfit deve refletir a soma da tabela
            // Atualizar diretamente o _completedSessionsProfit para refletir o total da tabela
            // Para operações não-dual, não há separação entre sessões
            _completedSessionsProfit = Math.Round(calculatedTotal, 2);

            _logger.LogInformation($"[TOTAL_PROFIT_UPDATE] Non-dual mode - Updated from table: {previousTotal:F2} -> {TotalProfit:F2}");
        }
        else
        {
            // Para modo dual, ajustar para evitar duplicação
            // TotalProfit = _completedSessionsProfit + SessionProfit
            // Então: _completedSessionsProfit = calculatedTotal - SessionProfit
            decimal adjustedCompletedSessions = Math.Round(calculatedTotal - SessionProfit, 2);
            _completedSessionsProfit = adjustedCompletedSessions;

            _logger.LogInformation($"[TOTAL_PROFIT_UPDATE] Dual mode - Adjusted to avoid duplication: {previousTotal:F2} -> {TotalProfit:F2}");
            _logger.LogInformation($"[TOTAL_PROFIT_UPDATE] Calculation: Table({calculatedTotal:F2}) - Session({SessionProfit:F2}) = Completed({_completedSessionsProfit:F2})");
        }

        _logger.LogInformation($"[TOTAL_PROFIT_UPDATE] Table sum: {calculatedTotal:F2}, Entries count: {ProfitTableEntries.Count}");

        // Logs finais para debugging
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] ===== FIM UpdateTotalProfitFromTable =====");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Final TotalProfit: {TotalProfit:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Final SessionProfit: {SessionProfit:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] Final CompletedSessions: {_completedSessionsProfit:F2}");
        _logger.LogInformation($"[UPDATE_TOTAL_DEBUG] TotalProfit change: {TotalProfit - previousTotal:F2}");

        // Notificar UI sobre a mudança
        OnPropertyChanged(nameof(TotalProfit));
    }

    /// <summary>
    /// Calcula as stakes x e y para o novo modo Dual conforme as fórmulas especificadas
    /// </summary>
    /// <returns>Tupla com (stakeX, stakeY) onde X é para contrato A e Y é para contrato B</returns>
    public (decimal stakeX, decimal stakeY) CalculateNewDualStakes()
    {
        try
        {
            // Sincronizar perdas acumuladas com TotalProfit antes do cálculo
            SyncDualPerdasWithTotalProfit();

            // Novo cálculo ancorado na stake MENOR do campo Stake (x) e na razão R = y/x
            // r: payout líquido por vitória
            const decimal r = 0.886m;

            // Entrada do usuário e limites
            decimal xRef = Math.Max(Stake, MinStakeAllowed); // stake menor desejada pelo usuário
            decimal D = Math.Max(0, DualPerdasAcumuladas);
            decimal Ld = Math.Max(0, DualAlfa * D + DualLucroBase); // lucro desejado por parcela

            // Interpretamos DualP como R = y/x (exibir como R na UI)
            decimal lowerR = 1m / r + 0.001m; // R > 1/r
            decimal R = Math.Max(DualP, lowerR);

            // Teto de perda por passo
            decimal Pcap = Math.Max(0, DualMaxLossAmount);

            // Valores base com x=xRef e y=R*xRef
            decimal y0 = R * xRef;
            decimal L0 = y0 * (r - 1m / R);
            decimal P0 = y0 * (1m - r / R);

            // Fator de escala s para atingir Ld sem ultrapassar Pcap
            decimal sL = (L0 > 0m && Ld > 0m) ? (Ld / L0) : 1m;
            if (sL < 1m) sL = 1m; // não reduzir a stake menor abaixo do escolhido
            decimal sP = (P0 > 0m && Pcap > 0m) ? (Pcap / P0) : decimal.MaxValue;
            decimal s = sL;
            if (s > sP) s = sP; // respeitar teto de perda

            // Aplicar escala
            decimal x = xRef * s;
            decimal y = R * x;

            // Arredondar e garantir mínimos
            x = Math.Round(Math.Max(x, MinStakeAllowed), 2);
            y = Math.Round(Math.Max(y, MinStakeAllowed), 2);

            // Revalidar P após arredondamento; se ultrapassar Pcap, reduzir x por passos de 0.01
            decimal P = y * (1m - r / R);
            if (Pcap > 0m && P > Pcap)
            {
                for (int i = 0; i < 5 && P > Pcap; i++) // até 5 passos de ajuste fino
                {
                    x = Math.Max(MinStakeAllowed, Math.Round(x - 0.01m, 2));
                    y = Math.Round(R * x, 2);
                    P = y * (1m - r / R);
                }
            }

            // Lucro e k com valores finais
            decimal L = y * (r - 1m / R);
            decimal k = (L > 0) ? P / L : 0m;

            _logger.LogInformation("[DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)");
            _logger.LogInformation($"[DUAL_STAKES] Parâmetros: Stake(x)={Stake:F2}, Alfa={DualAlfa:F2}, Perdas={D:F2}, Base={DualLucroBase:F2}, R(y/x)={R:F3}, Pcap={Pcap:F2}");
            _logger.LogInformation($"[DUAL_STAKES] Resultados: x={x:F2}, y={y:F2}, L={L:F4}, P={P:F4}, k={k:F3}");
            Console.WriteLine($"[DUAL_STAKES] x={x:F2}, y={y:F2}, L={L:F4}, P={P:F4}, k={k:F3}, R={R:F3}");

            return (x, y);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL_STAKES] Error calculating dual stakes");
            // Retornar stakes mínimas em caso de erro
            return (MinStakeAllowed, MinStakeAllowed);
        }
    }

    /// <summary>
    /// Atualiza as perdas acumuladas para o cálculo das próximas stakes
    /// </summary>
    /// <param name="profit">Lucro/prejuízo da operação</param>
    public void UpdateDualPerdasAcumuladas(decimal profit)
    {
        if (profit < 0)
        {
            // Adicionar prejuízo às perdas acumuladas
            DualPerdasAcumuladas += Math.Abs(profit);
            _logger.LogInformation($"[DUAL_PERDAS] Prejuízo de {Math.Abs(profit):F2} adicionado. Total acumulado: {DualPerdasAcumuladas:F2}");
        }
        else if (profit > 0)
        {
            // Reduzir perdas acumuladas com o lucro (recuperação)
            decimal reducao = Math.Min(DualPerdasAcumuladas, profit);
            DualPerdasAcumuladas -= reducao;
            _logger.LogInformation($"[DUAL_PERDAS] Lucro de {profit:F2} recuperou {reducao:F2}. Perdas restantes: {DualPerdasAcumuladas:F2}");
        }
    }

    /// <summary>
    /// Força a sincronização das perdas acumuladas com o TotalProfit atual
    /// Útil quando há discrepância entre os valores
    /// </summary>
    public void SyncDualPerdasWithTotalProfit()
    {
        // No modo dual com micro-metas, TotalProfit não reflete perdas; não sincronizar
        if (IsDualEnabled)
            return;

        if (TotalProfit < 0)
        {
            decimal totalLoss = Math.Abs(TotalProfit);
            if (Math.Abs(DualPerdasAcumuladas - totalLoss) > 0.01m) // Se há diferença significativa
            {
                _logger.LogWarning($"[DUAL_SYNC] Sincronizando perdas acumuladas - Antes: {DualPerdasAcumuladas:F2}, TotalProfit: {TotalProfit:F2}");
                DualPerdasAcumuladas = totalLoss;
                _logger.LogInformation($"[DUAL_SYNC] Perdas acumuladas sincronizadas para: {DualPerdasAcumuladas:F2}");
                Console.WriteLine($"[DUAL_SYNC] 🔄 Perdas sincronizadas: {DualPerdasAcumuladas:F2}");
            }
        }
        else
        {
            // Se TotalProfit é positivo, resetar perdas acumuladas
            if (DualPerdasAcumuladas > 0)
            {
                _logger.LogInformation($"[DUAL_SYNC] TotalProfit positivo ({TotalProfit:F2}) - resetando perdas acumuladas");
                DualPerdasAcumuladas = 0;
                Console.WriteLine($"[DUAL_SYNC] ✅ Perdas resetadas - TotalProfit positivo: {TotalProfit:F2}");
            }
        }
    }

    /// <summary>
    /// Micro-metas no modo dual: sempre que Profit/Sessão atingir o Lucro Base,
    /// transfere esse valor para o TotalProfit cumulativo e reduz o Profit/Sessão.
    /// Repete enquanto houver saldo suficiente. Para ao atingir o Lucro Alvo total.
    /// </summary>
    private void ProcessDualMicroPayouts()
    {
        if (!IsDualEnabled)
            return;

        decimal unit = Math.Max(0.01m, Math.Round(DualLucroBase, 2));
        bool transferred = false;

        // Transferir em passos da unidade configurada
        while (SessionProfit >= unit)
        {
            SessionProfit = Math.Round(SessionProfit - unit, 2);
            _completedSessionsProfit = Math.Round(_completedSessionsProfit + unit, 2);
            transferred = true;
            // Atualizar MaxLevel com os níveis usados até esta micrometa
            if (_dualLevelsInCurrentGoal > MaxLevel)
                MaxLevel = _dualLevelsInCurrentGoal;
            // Reset para próximo ciclo de micrometa
            _dualLevelsInCurrentGoal = 0;
        }

        if (transferred)
        {
            OnPropertyChanged(nameof(TotalProfit));
            _logger.LogInformation($"[MICRO] Transferência de micro-metas executada. Unit={unit:F2}, TotalProfit={TotalProfit:F2}, SessionProfit={SessionProfit:F2}");

            // Encerrar automaticamente quando bater o alvo total
            if (_completedSessionsProfit >= DualLucroAlvo)
            {
                _logger.LogInformation($"[MICRO] Lucro Alvo atingido por TotalProfit acumulado ({_completedSessionsProfit:F2} >= {DualLucroAlvo:F2}). Encerrando sessão.");
                CompleteSession();
            }
        }
    }

    // Method to verify TotalProfit accuracy
    public void VerifyTotalProfitCalculation()
    {
        decimal calculatedFromTable = CalculatedTotalProfitFromTable();
        decimal completedSessionsProfit = _completedSessionsProfit;
        decimal currentSessionProfit = SessionProfit;

        Console.WriteLine($"[PROFIT VERIFICATION] ===== DETAILED BREAKDOWN =====");
        Console.WriteLine($"[PROFIT VERIFICATION] CompletedSessionsProfit: {completedSessionsProfit:F2}");
        Console.WriteLine($"[PROFIT VERIFICATION] CurrentSessionProfit: {currentSessionProfit:F2}");
        Console.WriteLine($"[PROFIT VERIFICATION] Sum from Table: {calculatedFromTable:F2}");
        Console.WriteLine($"[PROFIT VERIFICATION] Expected Table = Completed + Current: {completedSessionsProfit + currentSessionProfit:F2}");
        Console.WriteLine($"[PROFIT VERIFICATION] Table Entries Count: {ProfitTableEntries.Count}");

        // Show individual entries
        for (int i = 0; i < Math.Min(ProfitTableEntries.Count, 5); i++)
        {
            var entry = ProfitTableEntries[i];
            Console.WriteLine($"[PROFIT VERIFICATION] Entry {i+1}: {entry.RefId} = {entry.TotalProfitLoss:F2}");
        }

        decimal expectedTable = completedSessionsProfit + currentSessionProfit;
        decimal difference = calculatedFromTable - expectedTable;

        if (Math.Abs(difference) > 0.01m)
        {
            Console.WriteLine($"[PROFIT WARNING] *** Table calculation MISMATCH! ***");
            Console.WriteLine($"[PROFIT WARNING] Difference: {difference:F2}");
        }
        else
        {
            Console.WriteLine($"[PROFIT VERIFICATION] ✓ Profit calculations are consistent");
        }
        Console.WriteLine($"[PROFIT VERIFICATION] =============================");
    }

    // Method to update Max Stake when a new stake is used
    private void UpdateMaxStake(decimal stake)
    {
        if (stake > MaxStake)
        {
            decimal oldMaxStake = MaxStake;
            MaxStake = stake;
            Console.WriteLine($"[MAX STAKE] New maximum stake: {oldMaxStake:F2} -> {MaxStake:F2}");
            _logger.LogInformation("New maximum stake recorded: {MaxStake:F2}", MaxStake);

            // CORREÇÃO: Verificar se estamos na thread da UI antes de usar Dispatcher
            if (Application.Current.Dispatcher.CheckAccess())
            {
                OnPropertyChanged(nameof(MaxStake));
            }
            else
            {
                Application.Current.Dispatcher.Invoke(() => OnPropertyChanged(nameof(MaxStake)));
            }

            // Salvar configuração quando MaxStake é atualizada
            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    /// <summary>
    /// Atualiza MaxStake baseado em todas as stakes da tabela de profit
    /// </summary>
    private void UpdateMaxStakeFromTable()
    {
        if (ProfitTableEntries?.Any() == true)
        {
            var maxStakeInTable = ProfitTableEntries.Max(e => e.Stake);

            // CORREÇÃO: Sempre atualizar MaxStake para o maior valor da tabela
            if (maxStakeInTable > MaxStake)
            {
                decimal oldMaxStake = MaxStake;
                MaxStake = maxStakeInTable;
                Console.WriteLine($"[MAX STAKE] Updated from table: {oldMaxStake:F2} -> {MaxStake:F2}");
                _logger.LogInformation($"Max stake updated from table: {maxStakeInTable:F2}");

                // CORREÇÃO: Verificar se estamos na thread da UI antes de usar Dispatcher
                if (Application.Current.Dispatcher.CheckAccess())
                {
                    OnPropertyChanged(nameof(MaxStake));
                }
                else
                {
                    Application.Current.Dispatcher.Invoke(() => OnPropertyChanged(nameof(MaxStake)));
                }

                // Salvar configuração
                _ = Task.Run(async () => await SaveConfigurationAsync());
            }

            // CORREÇÃO CRÍTICA: Forçar recálculo completo se MaxStake estiver incorreto
            if (MaxStake < maxStakeInTable)
            {
                decimal oldMaxStake = MaxStake;
                MaxStake = maxStakeInTable;
                Console.WriteLine($"[MAX STAKE CORRECTION] Force updated: {oldMaxStake:F2} -> {MaxStake:F2}");
                _logger.LogWarning($"Max stake was incorrect - force updated from {oldMaxStake:F2} to {maxStakeInTable:F2}");

                if (Application.Current.Dispatcher.CheckAccess())
                {
                    OnPropertyChanged(nameof(MaxStake));
                }
                else
                {
                    Application.Current.Dispatcher.Invoke(() => OnPropertyChanged(nameof(MaxStake)));
                }

                _ = Task.Run(async () => await SaveConfigurationAsync());
            }
        }
    }

    /// <summary>
    /// Força recálculo completo do MaxStake baseado na tabela
    /// </summary>
    public void RecalculateMaxStakeFromTable()
    {
        if (ProfitTableEntries?.Any() == true)
        {
            var maxStakeInTable = ProfitTableEntries.Max(e => e.Stake);
            decimal oldMaxStake = MaxStake;
            MaxStake = maxStakeInTable;

            Console.WriteLine($"[MAX STAKE RECALC] Recalculated: {oldMaxStake:F2} -> {MaxStake:F2}");
            _logger.LogInformation($"Max stake recalculated from table: {maxStakeInTable:F2}");

            if (Application.Current.Dispatcher.CheckAccess())
            {
                OnPropertyChanged(nameof(MaxStake));
            }
            else
            {
                Application.Current.Dispatcher.Invoke(() => OnPropertyChanged(nameof(MaxStake)));
            }

            _ = Task.Run(async () => await SaveConfigurationAsync());
        }
    }

    /// <summary>
    /// Inicializa MaxStake na inicialização da aplicação
    /// </summary>
    public void InitializeMaxStake()
    {
        if (ProfitTableEntries?.Any() == true)
        {
            var maxStakeInTable = ProfitTableEntries.Max(e => e.Stake);
            MaxStake = Math.Max(MaxStake, maxStakeInTable);

            Console.WriteLine($"[MAX STAKE INIT] Initialized to: {MaxStake:F2}");
            _logger.LogInformation($"Max stake initialized to: {MaxStake:F2}");

            OnPropertyChanged(nameof(MaxStake));
        }
        else
        {
            // Se não há entradas, garantir que MaxStake seja pelo menos a stake atual
            if (Stake > MaxStake)
            {
                MaxStake = Stake;
                OnPropertyChanged(nameof(MaxStake));
            }
        }
    }

    // Method to reset dual mode state completely
    private void ResetDualModeState()
    {
        _isDualEntryPending = false;
        _pendingDualContracts.Clear();
        _completedDualContracts = 0;
        CurrentDualLevel = 0;
        CurrentDualSession = 0;
        _losingContractTypeIndex = -1;
        _isFirstContractHigherStake = false;
        _accumulatedLoss = 0m;

        
        // Reset variáveis de recuperação
        _dualRecoveryAttempts = 0;
        _lastRecoveryAttempt = DateTime.MinValue;
        _isProcessingDualCompletion = false;
        
        Console.WriteLine("[DUAL RESET] Dual mode state completely reset");
        _logger.LogInformation("[DUAL RESET] All dual mode variables reset to initial state including recovery controls");
    }    // Controle de entradas duplas pendentes
    private bool _isDualEntryPending;
    private List<string> _pendingDualContracts = [];
    private int _completedDualContracts;
    private bool _isFirstContractHigherStake;
    private bool _isProcessingDualCompletion;
    private bool _isDualBuyInFlight;
    private int _dualLevelsInCurrentGoal;
    private decimal? _lastExitPrice;
    private DateTime? _lastExitTime;
    
    // Controle específico para cálculo dual
    private decimal _dualContract1Stake;
    private decimal _dualContract2Stake;
    private decimal _dualContract1Profit;
    private decimal _dualContract2Profit;
    private bool _dualContract1Completed;
    private bool _dualContract2Completed;
    private decimal _dualStakeHigher;
    private decimal _dualStakeLower;
    
    // Novo controle para o modo dual conforme requisitos
    private int _losingContractTypeIndex = -1; // 0 = primeiro contrato perdeu, 1 = segundo contrato perdeu
    private decimal _accumulatedLoss = 0m; // Prejuízo acumulado que precisa ser recuperado
    
    // Controle de recuperação robusta para DUAL RECOVERY
    private int _dualRecoveryAttempts = 0;
    private DateTime _lastRecoveryAttempt = DateTime.MinValue;
    private const int _maxRecoveryAttempts = 3;
    private const int _recoveryDelayMs = 5000; // 5 segundos entre tentativas
    
    // Controle para atualização do saldo inicial
    private bool _userClickedStop = false; // Rastreia se o usuário clicou em Stop

    // Lista completa de símbolos ativos para filtragem
    private List<ActiveSymbol> _allActiveSymbols = [];
    
    // Flag to prevent clearing during restoration
    private bool _isRestoringSelections = false;
    
    // Connection state preservation
    private bool _wasInDualMode = false;
    private bool _wasTradingActive = false;
    
    // Chart functionality
    private ChartViewModel _chartViewModel;
    public ChartViewModel ChartViewModel
    {
        get => _chartViewModel;
        set { _chartViewModel = value; OnPropertyChanged(); }
    }
    
    // Chart zoom commands
    public ICommand ZoomToFiveMinutesCommand { get; private set; }
    public ICommand ZoomToFifteenMinutesCommand { get; private set; }
    public ICommand ZoomToOneHourCommand { get; private set; }
    public ICommand ZoomToAllCommand { get; private set; }
    
    // Chart time unit commands
    public ICommand ChartTimeUnitTicksCommand { get; private set; }
    public ICommand ChartTimeUnitSecondsCommand { get; private set; }
    public ICommand ChartTimeUnitMinutesCommand { get; private set; }
    
    public MainViewModel(IDerivApiService derivApiService, ILogger<MainViewModel> logger, IUserConfigurationService configService)
    {
        _derivApiService = derivApiService;
        _logger = logger;
        _configService = configService;
        _chartViewModel = new ChartViewModel();
        
        // Initialize chart zoom commands
        ZoomToFiveMinutesCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(5));
        ZoomToFifteenMinutesCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(15));
        ZoomToOneHourCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(60));
        ZoomToAllCommand = new RelayCommand(() => ChartViewModel?.ResetZoom());
        
        // Initialize chart time unit commands
        ChartTimeUnitTicksCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("ticks"));
        ChartTimeUnitSecondsCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("seconds"));
        ChartTimeUnitMinutesCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("minutes"));
        
        SubscribeToApiEvents();
        
        // Carregar configuração salva antes de conectar
        _ = LoadConfigurationAsync();
        
        // Inicializar timer de pausa automática
        InitializeAutoPauseTimer();
        
        _derivApiService.ConnectAndAuthorizeAsync();
    }

    private void SubscribeToApiEvents()
    {
        _derivApiService.ConnectionEstablished += OnConnectionEstablished;
        _derivApiService.ConnectionLost += OnConnectionLost;
        _derivApiService.AccountInfoUpdated += OnAccountInfoUpdated;
        _derivApiService.PingUpdated += OnPingUpdated;
        _derivApiService.ContractResult += OnContractResultReceived;
        _derivApiService.ContractNearExpiry += OnContractNearExpiry;
        _derivApiService.TickReceived += OnTickReceived;
        _derivApiService.ContractFinished += OnContractFinished;
        _derivApiService.ContractPurchased += OnContractPurchased;
        _derivApiService.ContractEntryTickReceived += OnContractEntryTickReceived;
    }

    private void OnContractEntryTickReceived(string contractId, decimal entryTick, long entryTickTime)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId);
            if (entry != null)
            {
                // proposal_open_contract is authoritative; always update entry spot/price when received
                entry.EntryPrice = entryTick;
                entry.EntrySpot = DateTimeOffset.FromUnixTimeSeconds(entryTickTime).UtcDateTime;
                _logger.LogInformation("Profit Table entry updated with official entry tick for contract {ContractId}: {EntryTick} at {EntrySpot:HH:mm:ss}", contractId, entryTick, entry.EntrySpot);
            }
        });
    }

    private void OnPingUpdated(long newPing)
    {
        Application.Current.Dispatcher.Invoke(() => Ping = newPing);
    }

    private void OnContractResultReceived(bool isWin)
    {
        var receivedTime = DateTimeOffset.Now;
        
        // Atualizar contador de perdas consecutivas para pausa automática
        UpdateConsecutiveLossesForPause(isWin);
        
        // Verificação de condições de pausa automática removida
        
        // Verificação de pausa automática removida
        
        // ULTRA-IMMEDIATE EXECUTION: Zero overhead processing
        if (IsMartingaleEnabled)
        {
            if (isWin)
            {
                _logger.LogInformation("[TIMING] Contract WIN at {ReceivedTime:HH:mm:ss.fff} - calling OnContractWin", receivedTime);
                // Fire-and-forget for win processing
                _ = Task.Run(() => OnContractWin());
            }
            else
            {
                _logger.LogInformation("[TIMING] Contract LOSS at {ReceivedTime:HH:mm:ss.fff} - ZERO-DELAY EXECUTION", receivedTime);
                // ZERO-DELAY EXECUTION: Inline call with no overhead
                var executionStart = DateTimeOffset.Now;
                OnContractLossUltraFast();
                var executionEnd = DateTimeOffset.Now;
                var totalExecution = (executionEnd - executionStart).TotalMilliseconds;
                _logger.LogInformation("[TIMING] ZERO-DELAY: Complete execution in {TotalExecution}ms", totalExecution);
            }
        }
        else if (IsDualEnabled)
        {
            _logger.LogInformation("[DUAL] Contract result received at {ReceivedTime:HH:mm:ss.fff} - WIN: {IsWin}", receivedTime, isWin);
            OnDualContractResult(isWin);
        }
        else
        {
            _logger.LogInformation("[DEBUG] Neither Martingale nor Dual enabled, ignoring contract result");
        }
    }

    private void OnContractNearExpiry(string contractId)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            _logger.LogInformation("[DEBUG] OnContractNearExpiry chamado para contrato: {ContractId}, IsMartingaleEnabled = {IsMartingaleEnabled}, IsFastMartingale = {IsFastMartingale}", contractId, IsMartingaleEnabled, IsFastMartingale);
            
            // Ensure hot pool is ready for instant execution if needed
            if (IsMartingaleEnabled && IsFastMartingale)
            {
                _logger.LogInformation($"[DEBUG] Contrato próximo ao vencimento - garantindo HOT POOL pronto para execução instantânea");
                
                // Trigger immediate pool verification and replenishment if needed
                _ = Task.Run(async () =>
                {
                    lock (_poolLock)
                    {
                        var availableProposals = _hotProposalPool.Count;
                        _logger.LogInformation("[DEBUG] HOT POOL status: {AvailableProposals} propostas disponíveis", availableProposals);
                        
                        if (availableProposals < 2)
                        {
                            _logger.LogInformation("[DEBUG] HOT POOL com poucas propostas - iniciando reabastecimento de emergencia");
                        }
                    }
                    
                    // Always ensure pool is fully populated before potential loss
                    await PopulateHotProposalPool();
                });
            }
            else if (IsDualEnabled)
            {
                // Pré-preparar e assinar propostas do próximo par (streams quentes)
                _logger.LogInformation("[DUAL PREP] Contrato próximo da expiração - preparando subscriptions de propostas");
                PrepareNextDualRequests();
                StartDualProposalSubscriptions();
            }
        });
    }
    
    // Tick update event handler
    private void OnTickReceived(decimal price, DateTime timestamp)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            CurrentTickPrice = price;
            LastTickTime = timestamp;

            // Enable continuous auto-scroll to ensure latest data is always visible
            ChartViewModel?.EnableContinuousAutoScroll();

            // Update chart with new tick data
            ChartViewModel?.AddTickData(price, timestamp);

            // Update active profit table entries with current price
            UpdateActiveProfitTableEntries(price);
        });
    }


    private void OnAccountInfoUpdated(string accountCode, string? accountType, double balance)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            AccountCode = accountCode;
            // O tipo de conta só vem na autorização, então não atualizamos se vier nulo ou vazio
            if (!string.IsNullOrEmpty(accountType)) 
            {
                AccountType = accountType;
            }
            
            double oldBalance = Balance;
            var expectedBalance = CalculateExpectedBalance();
            var activeExposure = ActiveExposure;
            
            _logger.LogInformation($"[BALANCE_UPDATE] Estado antes da atualização:");
            _logger.LogInformation($"[BALANCE_UPDATE] - Balance atual: {oldBalance:F2}");
            _logger.LogInformation($"[BALANCE_UPDATE] - Balance esperado: {expectedBalance:F2}");
            _logger.LogInformation($"[BALANCE_UPDATE] - SessionProfit: {SessionProfit:F2}");
            _logger.LogInformation($"[BALANCE_UPDATE] - CompletedSessionsProfit: {_completedSessionsProfit:F2}");
            _logger.LogInformation($"[BALANCE_UPDATE] - ActiveExposure: {activeExposure:F2}");
            
            Balance = balance;
            var balanceDifference = balance - oldBalance;
            
            _logger.LogInformation($"[BALANCE_UPDATE] Novo balance da API: {balance:F2}");
            _logger.LogInformation($"[BALANCE_UPDATE] Diferença no balance: {balanceDifference:F2}");
            
            // Inicializar InitialBalance na primeira vez que o balance é recebido
            if (InitialBalance == 0)
            {
                InitialBalance = balance;
                _logger.LogInformation("[BALANCE] Initial balance set to: {Balance:F2}", balance);
            }
            else
            {
                // Validar consistência do saldo usando método centralizado
                ValidateBalanceConsistency(balance, "Account Info Update");
                
                Console.WriteLine($"[BALANCE UPDATE] API Balance: {oldBalance:F2} -> {balance:F2}");
            }
        });
    }

    private void OnConnectionLost()
    {
        Application.Current.Dispatcher.Invoke(() => 
        {
            IsConnected = false;
            _logger.LogWarning("[CONNECTION] Connection lost - preserving current state for restoration");
            
            // Preserve current trading state for restoration
            _wasInDualMode = IsDualEnabled;
            _wasTradingActive = _pendingDualContracts.Count > 0;
            
            // Reset transient pending dual state to avoid stuck UI while offline
            _isDualEntryPending = false;
            _completedDualContracts = 0;
            
            // CRITICAL: Save current dual trading state immediately
            if (IsDualEnabled && (CurrentDualSession > 1 || CurrentDualLevel > 0 || _pendingDualContracts.Count > 0))
            {
                _logger.LogWarning("[CRITICAL STATE] Preserving active dual session - Session: {CurrentDualSession}, Level: {CurrentDualLevel}, Pending: {PendingCount}", CurrentDualSession, CurrentDualLevel, _pendingDualContracts.Count);
                
                // Force immediate save of critical state
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await SaveConfigurationAsync();
                        _logger.LogInformation("[CRITICAL STATE] Emergency state save completed");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL STATE] Failed to save emergency state");
                    }
                });
            }
            
            // Log current state for debugging
            Console.WriteLine($"[CONNECTION LOST] Dual mode: {_wasInDualMode}, Active contracts: {_pendingDualContracts.Count}, Session: {CurrentDualSession}, Level: {CurrentDualLevel}");
        });
    }

    private void OnConnectionEstablished()
    {
        Application.Current.Dispatcher.Invoke(() => 
        {
            _logger.LogInformation("[CONNECTION] Connection reestablished - restoring application state");
            IsConnected = true;
            
            // ENHANCED: Always force load configuration first to ensure we have the latest saved state
            _ = Task.Run(async () =>
            {
                try
                {
                    // STEP 1: Force reload configuration to get latest saved state
                    _logger.LogInformation("[RESTORE] Step 1: Force loading latest configuration");
                    await LoadConfigurationAsync();
                    
                    // STEP 2: Determine what to restore
                    var previousMarket = SelectedMarket ?? _pendingMarketSelection;
                    var previousSubMarket = SelectedSubMarket ?? _pendingSubMarketSelection;
                    var previousActiveSymbol = SelectedActiveSymbol?.Symbol ?? _pendingActiveSymbolSelection;
                    var previousContractType = SelectedContractType?.ContractType ?? _pendingContractTypeSelection;
                    var previousDualContractType = SelectedDualContractType?.ContractType ?? _pendingDualContractTypeSelection;
                    
                    _logger.LogInformation("[RESTORE] State to restore - Market: {PreviousMarket}, Symbol: {PreviousActiveSymbol}, Contract: {PreviousContractType}, DualContract: {PreviousDualContractType}", previousMarket, previousActiveSymbol, previousContractType, previousDualContractType);
                    
                    // STEP 3: Load active symbols
                    _logger.LogInformation("[RESTORE] Step 2: Loading active symbols");
                    await LoadActiveSymbolsAsync();
                    
                    // STEP 4: Restore selections with retry mechanism
                    _logger.LogInformation("[RESTORE] Step 3: Restoring selections with retry");
                    await RestoreSelectionsWithRetry(previousMarket, previousSubMarket, previousActiveSymbol, previousContractType, previousDualContractType);
                    
                    // STEP 5: Handle dual mode restoration
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        if (_wasInDualMode && IsDualEnabled)
                        {
                            _logger.LogInformation("[DUAL] Connection restored during active trading - Session: {CurrentDualSession}, Level: {CurrentDualLevel}", CurrentDualSession, CurrentDualLevel);
                            
                            // Check if we should auto-resume trading
                            if (CurrentDualLevel > 0 && IsTradingEnabled)
                            {
                                _logger.LogWarning("[DUAL AUTO-RESUME] Attempting to resume trading after reconnection");
                                
                                // Auto-resume with delay to ensure everything is loaded
                                _ = Task.Run(async () =>
                                {
                                    await Task.Delay(3000); // Give time for full restoration
                                    
                                    await Application.Current.Dispatcher.InvokeAsync(async () =>
                                    {
                                        // Auto-reativar trading se estava em modo dual ativo
                                        if (IsDualEnabled && !IsTradingEnabled && IsConnected && CurrentDualLevel > 0)
                                        {
                                            _logger.LogInformation("[AUTO-RESUME] Reativando trading automaticamente após reconexão");
                                            IsTradingEnabled = true;
                                        }
                                        
                                        if (IsDualEnabled && IsTradingEnabled && IsConnected && SelectedContractType != null && SelectedDualContractType != null)
                                        {
                                            _logger.LogInformation("[DUAL AUTO-RESUME] Conditions met, resuming dual trading");
                                            Console.WriteLine($"[DUAL AUTO-RESUME] Resuming at Level {CurrentDualLevel}");
                                            
                                            try
                                            {
                                                await ExecuteDualEntryCommand();
                                            }
                                            catch (Exception ex)
                                            {
                                                _logger.LogError(ex, "[DUAL AUTO-RESUME] Failed to auto-resume trading");
                                            }
                                        }
                                        else
                                        {
                                            _logger.LogWarning("[DUAL AUTO-RESUME] Conditions not met for auto-resume");
                                            _logger.LogWarning($"[DUAL AUTO-RESUME] IsDualEnabled: {IsDualEnabled}, IsTradingEnabled: {IsTradingEnabled}, IsConnected: {IsConnected}");
                                            _logger.LogWarning($"[DUAL AUTO-RESUME] SelectedContractType: {SelectedContractType?.ContractType}, SelectedDualContractType: {SelectedDualContractType?.ContractType}");
                                            // Reset any stale pending state to avoid UI stuck as 'pendente'
                                            _isDualEntryPending = false;
                                            _pendingDualContracts.Clear();
                                            _completedDualContracts = 0;
                                        }
                                    });
                                });
                            }
                        }
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[RESTORE] Failed during connection restoration");
                }
            });
        });
    }

    // Emergency method to restore contract types when normal restoration fails
    private async Task EmergencyContractRestoration(string? previousContractType, string? previousDualContractType)
    {
        try
        {
            _logger.LogInformation("[EMERGENCY RESTORE] Starting emergency contract restoration");
            
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // Force reload contract types if we have a selected symbol
                if (SelectedActiveSymbol != null)
                {
                    _logger.LogInformation($"[EMERGENCY RESTORE] Reloading contracts for symbol: {SelectedActiveSymbol.Symbol}");
                    
                    // Clear and reload contract types
                    ContractTypes.Clear();
                    DualContractTypes.Clear();
                    
                    try
                    {
                        var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedActiveSymbol.Symbol);
                        
                        foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
                        {
                            ContractTypes.Add(contract);
                            DualContractTypes.Add(contract);
                        }
                        
                        _logger.LogInformation($"[EMERGENCY RESTORE] Loaded {ContractTypes.Count} contract types");
                        
                        // Try to restore contract selections
                        if (!string.IsNullOrEmpty(previousContractType))
                        {
                            var contractToRestore = ContractTypes.FirstOrDefault(c => c.ContractType == previousContractType);
                            if (contractToRestore != null)
                            {
                                SelectedContractType = contractToRestore;
                                _logger.LogInformation($"[EMERGENCY RESTORE] Main contract type restored: {previousContractType}");
                            }
                            else
                            {
                                _logger.LogError($"[EMERGENCY RESTORE] Failed to find main contract type: {previousContractType}");
                            }
                        }
                        
                        if (!string.IsNullOrEmpty(previousDualContractType))
                        {
                            var dualContractToRestore = ContractTypes.FirstOrDefault(c => c.ContractType == previousDualContractType);
                            if (dualContractToRestore != null)
                            {
                                SelectedDualContractType = dualContractToRestore;
                                _logger.LogInformation($"[EMERGENCY RESTORE] Dual contract type restored: {previousDualContractType}");
                            }
                            else
                            {
                                _logger.LogError($"[EMERGENCY RESTORE] Failed to find dual contract type: {previousDualContractType}");
                            }
                        }
                        
                        Console.WriteLine("[EMERGENCY RESTORE] Emergency restoration completed");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[EMERGENCY RESTORE] Failed to reload contract types");
                    }
                }
                else
                {
                    _logger.LogWarning("[EMERGENCY RESTORE] No selected symbol - cannot reload contract types");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[EMERGENCY RESTORE] Critical error in emergency restoration");
        }
    }

    // Métodos para carregar dados da API
    private async Task LoadActiveSymbolsAsync()
    {
        try
        {
            _logger.LogInformation("[DEBUG] LoadActiveSymbolsAsync iniciado");
            var symbols = await _derivApiService.GetActiveSymbolsAsync();
            _logger.LogInformation($"[DEBUG] Recebidos {symbols.Count} símbolos ativos");
            _allActiveSymbols = symbols;
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                // Extrai mercados únicos
                var markets = symbols.Select(s => s.MarketDisplayName).Distinct().OrderBy(m => m).ToList();
                _logger.LogInformation($"[DEBUG] Extraídos {markets.Count} mercados únicos");
                Markets.Clear();
                foreach (var market in markets)
                {
                    Markets.Add(market);
                    _logger.LogInformation($"[DEBUG] Adicionado mercado: {market}");
                }
                _logger.LogInformation($"[DEBUG] Markets.Count após carregamento: {Markets.Count}");
                _logger.LogInformation($"[DEBUG] Markets contém: {string.Join(", ", Markets)}");
                
                // Força notificação de mudança na propriedade Markets
                OnPropertyChanged(nameof(Markets));
                
                // Aplicar seleções pendentes da configuração salva
                ApplyPendingSelections();
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro em LoadActiveSymbolsAsync: {ex.Message}");
            // Log do erro - em uma implementação real, você usaria um logger
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar símbolos ativos: {ex.Message}");
        }
    }

    private async Task RestoreSelectionsAsync(string? previousMarket, string? previousSubMarket, string? previousActiveSymbol, string? previousContractType, string? previousDualContractType = null)
    {
        try
        {
            _isRestoringSelections = true;
            _logger.LogInformation($"[RESTORE] Starting selection restoration - Market: {previousMarket}, Symbol: {previousActiveSymbol}, Contract: {previousContractType}, Dual: {previousDualContractType}");
            
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // Step 1: Wait for markets to be fully loaded with extended timeout
                int marketWaitAttempts = 0;
                while (Markets.Count == 0 && marketWaitAttempts < 40) // Increased from 20 to 40
                {
                    _logger.LogInformation($"[RESTORE] Waiting for markets to load... attempt {marketWaitAttempts + 1}/40");
                    await Task.Delay(300); // Increased delay
                    marketWaitAttempts++;
                }
                
                if (Markets.Count == 0)
                {
                    _logger.LogError("[RESTORE] Failed to load markets after 40 attempts - cannot restore selections");
                    return;
                }
                
                _logger.LogInformation($"[RESTORE] Markets loaded successfully: {string.Join(", ", Markets)}");
                
                // Step 2: Restore Market with better logging
                if (!string.IsNullOrEmpty(previousMarket))
                {
                    var marketToRestore = Markets.FirstOrDefault(m => string.Equals(m, previousMarket, StringComparison.OrdinalIgnoreCase));
                    if (marketToRestore != null)
                    {
                        SelectedMarket = marketToRestore;
                        _logger.LogInformation($"[RESTORE] ✓ Market restored: '{previousMarket}' -> '{SelectedMarket}'");
                        
                        // Step 3: Wait for and restore SubMarket
                        int subMarketAttempts = 0;
                        while (SubMarkets.Count == 0 && subMarketAttempts < 20)
                        {
                            _logger.LogInformation($"[RESTORE] Waiting for submarkets to load... attempt {subMarketAttempts + 1}/20");
                            await Task.Delay(400);
                            subMarketAttempts++;
                        }
                        
                        if (!string.IsNullOrEmpty(previousSubMarket))
                        {
                            var subMarketToRestore = SubMarkets.FirstOrDefault(sm => string.Equals(sm, previousSubMarket, StringComparison.OrdinalIgnoreCase));
                            if (subMarketToRestore != null)
                            {
                                SelectedSubMarket = subMarketToRestore;
                                _logger.LogInformation($"[RESTORE] ✓ SubMarket restored: '{previousSubMarket}' -> '{SelectedSubMarket}'");
                                
                                // Step 4: Wait for and restore Active Symbol
                                int symbolAttempts = 0;
                                while (ActiveSymbols.Count == 0 && symbolAttempts < 20)
                                {
                                    _logger.LogInformation($"[RESTORE] Waiting for symbols to load... attempt {symbolAttempts + 1}/20");
                                    await Task.Delay(400);
                                    symbolAttempts++;
                                }
                                
                                if (!string.IsNullOrEmpty(previousActiveSymbol))
                                {
                                    var symbolToRestore = ActiveSymbols.FirstOrDefault(s => string.Equals(s.Symbol, previousActiveSymbol, StringComparison.OrdinalIgnoreCase));
                                    if (symbolToRestore != null)
                                    {
                                        SelectedActiveSymbol = symbolToRestore;
                                        _logger.LogInformation($"[RESTORE] ✓ Symbol restored: '{previousActiveSymbol}' -> '{SelectedActiveSymbol?.Symbol}'");
                                        
                                        // Step 5: Wait for and restore Contract Types with multiple attempts
                                        await RestoreContractTypesWithRetry(previousContractType, previousDualContractType);
                                    }
                                    else
                                    {
                                        _logger.LogError($"[RESTORE] ✗ Symbol '{previousActiveSymbol}' not found in {ActiveSymbols.Count} available symbols: {string.Join(", ", ActiveSymbols.Take(5).Select(s => s.Symbol))}...");
                                    }
                                }
                            }
                            else
                            {
                                _logger.LogError($"[RESTORE] ✗ SubMarket '{previousSubMarket}' not found in {SubMarkets.Count} available: {string.Join(", ", SubMarkets)}");
                            }
                        }
                    }
                    else
                    {
                        _logger.LogError($"[RESTORE] ✗ Market '{previousMarket}' not found in {Markets.Count} available: {string.Join(", ", Markets)}");
                    }
                }
                
                // Final verification
                _logger.LogInformation($"[RESTORE] Final restoration state:");
                _logger.LogInformation($"[RESTORE]   Market: '{previousMarket}' -> '{SelectedMarket}' {(SelectedMarket == previousMarket ? "✓" : "✗")}");
                _logger.LogInformation($"[RESTORE]   SubMarket: '{previousSubMarket}' -> '{SelectedSubMarket}' {(SelectedSubMarket == previousSubMarket ? "✓" : "✗")}");
                _logger.LogInformation($"[RESTORE]   Symbol: '{previousActiveSymbol}' -> '{SelectedActiveSymbol?.Symbol}' {(SelectedActiveSymbol?.Symbol == previousActiveSymbol ? "✓" : "✗")}");
                _logger.LogInformation($"[RESTORE]   Contract: '{previousContractType}' -> '{SelectedContractType?.ContractType}' {(SelectedContractType?.ContractType == previousContractType ? "✓" : "✗")}");
                _logger.LogInformation($"[RESTORE]   DualContract: '{previousDualContractType}' -> '{SelectedDualContractType?.ContractType}' {(SelectedDualContractType?.ContractType == previousDualContractType ? "✓" : "✗")}");
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[RESTORE] Critical error during selection restoration: {ex.Message}");
        }
        finally
        {
            _isRestoringSelections = false;
        }
    }
    
    private async Task RestoreContractTypesWithRetry(string? previousContractType, string? previousDualContractType)
    {
        const int maxAttempts = 25; // Increased attempts for contract types
        
        // Restore main contract type
        if (!string.IsNullOrEmpty(previousContractType))
        {
            bool contractRestored = false;
            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                var contractToRestore = ContractTypes.FirstOrDefault(c => string.Equals(c.ContractType, previousContractType, StringComparison.OrdinalIgnoreCase));
                if (contractToRestore != null)
                {
                    SelectedContractType = contractToRestore;
                    _logger.LogInformation($"[RESTORE] ✓ Main contract type restored: '{previousContractType}' (attempt {attempt})");
                    contractRestored = true;
                    break;
                }
                
                if (ContractTypes.Count == 0)
                {
                    _logger.LogInformation($"[RESTORE] Contract types list empty, waiting... attempt {attempt}/{maxAttempts}");
                }
                else
                {
                    _logger.LogWarning($"[RESTORE] Contract type '{previousContractType}' not found in {ContractTypes.Count} available types, attempt {attempt}/{maxAttempts}");
                    _logger.LogInformation($"[RESTORE] Available contract types: {string.Join(", ", ContractTypes.Select(c => c.ContractType))}");
                }
                
                await Task.Delay(300); // Wait before retry
            }
            
            if (!contractRestored)
            {
                _logger.LogError($"[RESTORE] ✗ FAILED to restore main contract type '{previousContractType}' after {maxAttempts} attempts");
            }
        }
        
        // Restore dual contract type
        if (!string.IsNullOrEmpty(previousDualContractType))
        {
            bool dualContractRestored = false;
            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                var dualContractToRestore = ContractTypes.FirstOrDefault(c => string.Equals(c.ContractType, previousDualContractType, StringComparison.OrdinalIgnoreCase));
                if (dualContractToRestore != null)
                {
                    SelectedDualContractType = dualContractToRestore;
                    _logger.LogInformation($"[RESTORE] ✓ Dual contract type restored: '{previousDualContractType}' (attempt {attempt})");
                    dualContractRestored = true;
                    break;
                }
                
                if (ContractTypes.Count == 0)
                {
                    _logger.LogInformation($"[RESTORE] Contract types list empty for dual restore, waiting... attempt {attempt}/{maxAttempts}");
                }
                else
                {
                    _logger.LogWarning($"[RESTORE] Dual contract type '{previousDualContractType}' not found, attempt {attempt}/{maxAttempts}");
                }
                
                await Task.Delay(300);
            }
            
            if (!dualContractRestored)
            {
                _logger.LogError($"[RESTORE] ✗ FAILED to restore dual contract type '{previousDualContractType}' after {maxAttempts} attempts");
            }
        }
    }

    // Enhanced restoration method with robust retry mechanism
    private async Task RestoreSelectionsWithRetry(string? previousMarket, string? previousSubMarket, string? previousActiveSymbol, string? previousContractType, string? previousDualContractType)
    {
        const int maxRetries = 5;
        
        for (int retry = 1; retry <= maxRetries; retry++)
        {
            try
            {
                _logger.LogInformation($"[RESTORE RETRY] Attempt {retry}/{maxRetries} - Starting restoration");
                
                // Try the normal restoration process
                await RestoreSelectionsAsync(previousMarket, previousSubMarket, previousActiveSymbol, previousContractType, previousDualContractType);
                
                // Wait for restoration to complete
                await Task.Delay(1000);
                
                // Verify restoration success
                bool success = await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    bool marketOk = string.IsNullOrEmpty(previousMarket) || SelectedMarket == previousMarket;
                    bool symbolOk = string.IsNullOrEmpty(previousActiveSymbol) || SelectedActiveSymbol?.Symbol == previousActiveSymbol;
                    bool contractOk = string.IsNullOrEmpty(previousContractType) || SelectedContractType?.ContractType == previousContractType;
                    bool dualContractOk = string.IsNullOrEmpty(previousDualContractType) || SelectedDualContractType?.ContractType == previousDualContractType;
                    
                    _logger.LogInformation($"[RESTORE VERIFY] Market: {marketOk}, Symbol: {symbolOk}, Contract: {contractOk}, DualContract: {dualContractOk}");
                    
                    return marketOk && symbolOk && contractOk && dualContractOk;
                });
                
                if (success)
                {
                    _logger.LogInformation($"[RESTORE RETRY] ✓ Restoration successful on attempt {retry}");
                    return;
                }
                else
                {
                    _logger.LogWarning($"[RESTORE RETRY] ✗ Restoration verification failed on attempt {retry}");
                    
                    if (retry < maxRetries)
                    {
                        await Task.Delay(2000); // Wait longer between retries
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[RESTORE RETRY] Exception on attempt {retry}/{maxRetries}");
                
                if (retry < maxRetries)
                {
                    await Task.Delay(2000);
                }
            }
        }
        
        _logger.LogError($"[RESTORE RETRY] ✗✗✗ COMPLETE FAILURE after {maxRetries} attempts - Manual intervention required");
        
        // Last resort: Force emergency restoration
        try
        {
            _logger.LogWarning("[RESTORE EMERGENCY] Attempting emergency restoration as last resort");
            await EmergencyContractRestoration(previousContractType, previousDualContractType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[RESTORE EMERGENCY] Emergency restoration also failed");
        }
    }

    // Métodos para lógica de seleção em cascata
    private void OnMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedMarket))
        {
            if (!_isRestoringSelections)
            {
                SubMarkets.Clear();
                ActiveSymbols.Clear();
                ContractTypes.Clear();
            }
            return;
        }

        // Filtra submercados baseado no mercado selecionado
        var subMarkets = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket)
            .Select(s => s.SubmarketDisplayName)
            .Distinct()
            .OrderBy(sm => sm)
            .ToList();

        SubMarkets.Clear();
        foreach (var subMarket in subMarkets)
        {
            SubMarkets.Add(subMarket);
        }

        // Limpa seleções subsequentes apenas se não estiver restaurando
        if (!_isRestoringSelections)
        {
            SelectedSubMarket = null;
            ActiveSymbols.Clear();
            ContractTypes.Clear();
        }
    }

    private void OnSubMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedSubMarket))
        {
            if (!_isRestoringSelections)
            {
                ActiveSymbols.Clear();
                ContractTypes.Clear();
            }
            return;
        }

        // Filtra símbolos ativos baseado no submercado selecionado
        var symbols = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket && s.SubmarketDisplayName == SelectedSubMarket)
            .OrderBy(s => s.DisplayName)
            .ToList();

        ActiveSymbols.Clear();
        foreach (var symbol in symbols)
        {
            ActiveSymbols.Add(symbol);
        }

        // Limpa seleções subsequentes apenas se não estiver restaurando
        if (!_isRestoringSelections)
        {
            SelectedActiveSymbol = null;
            ContractTypes.Clear();
        }
    }

    private async void OnActiveSymbolSelectionChanged()
    {
        if (SelectedActiveSymbol == null)
        {
            if (!_isRestoringSelections)
            {
                ContractTypes.Clear();
                DualContractTypes.Clear();
            }
            return;
        }

        try
        {
            var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedActiveSymbol.Symbol);
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                ContractTypes.Clear();
                DualContractTypes.Clear();
                
                // Only clear SelectedContractType if not restoring
                if (!_isRestoringSelections)
                {
                    SelectedContractType = null; // Isso vai chamar o UpdateContractParameters e limpar a UI
                    SelectedDualContractType = null;
                }
                
                foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
                {
                    ContractTypes.Add(contract);
                    DualContractTypes.Add(contract);
                }
            });
        }
        catch (Exception ex)
        {
            // Log do erro
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar contratos para {SelectedActiveSymbol.Symbol}: {ex.Message}");
        }
    }

    private void PopulateDualContractTypes()
    {
        if (SelectedActiveSymbol != null && ContractTypes.Any())
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                DualContractTypes.Clear();
                foreach (var contract in ContractTypes)
                {
                    DualContractTypes.Add(contract);
                }
            });
        }
    }

    // Lógica para atualizar a UI com base no contrato selecionado
    private void UpdateContractParameters()
    {
        if (SelectedContractType == null)
        {
            IsDurationVisible = false;
            IsBarrier1Visible = false;
            IsBarrier2Visible = false;
            IsDigitSelectionVisible = false;
            DurationInfo = string.Empty;
            Barrier1Suggestion = string.Empty;
            Barrier2Suggestion = string.Empty;
            return;
        }

        // Reset
        IsBarrier1Visible = false;
        IsBarrier2Visible = false;
        IsDigitSelectionVisible = false;
        Barrier1Suggestion = string.Empty;
        Barrier2Suggestion = string.Empty;

        // Sempre mostrar duração
        IsDurationVisible = true;
        DurationInfo = $"Duração: Min {SelectedContractType.MinContractDuration}, Max {SelectedContractType.MaxContractDuration}";

        // Verificar Barreiras e definir sugestões
        if (SelectedContractType.Barriers.HasValue)
        {
            if (SelectedContractType.Barriers >= 1)
            {
                IsBarrier1Visible = true;
                Barrier1Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 1);
            }
            if (SelectedContractType.Barriers == 2)
            {
                IsBarrier2Visible = true;
                Barrier2Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 2);
            }
        }

        // Verificar Dígitos
        if (SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
        {
            IsDigitSelectionVisible = true;
        }

        // Recalcular proposta quando parâmetros mudarem
        CalculateProposalAsync();
    }

    private string GetBarrierSuggestion(string contractType, int barrierNumber)
    {
        return contractType?.ToUpper() switch
        {
            "HIGHER" => "+10.5",
            "LOWER" => "-10.5",
            "TOUCH" => "+15.0",
            "NOTOUCH" => "+20.0",
            "STAYS_IN" when barrierNumber == 1 => "+25.0",
            "STAYS_IN" when barrierNumber == 2 => "-25.0",
            "GOES_OUT" when barrierNumber == 1 => "+30.0",
            "GOES_OUT" when barrierNumber == 2 => "-30.0",
            "ENDS_IN" when barrierNumber == 1 => "+20.0",
            "ENDS_IN" when barrierNumber == 2 => "-20.0",
            "ENDS_OUT" when barrierNumber == 1 => "+35.0",
            "ENDS_OUT" when barrierNumber == 2 => "-35.0",
            _ => "+10.0"
        };
    }

    private async Task CalculateProposalAndBuyAsync()
    {
        var methodStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] CalculateProposalAndBuyAsync iniciado às {methodStartTime:HH:mm:ss.fff}");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null)
        {
            _logger.LogInformation("[TIMING] CalculateProposalAndBuyAsync cancelado - dados insuficientes");
            return;
        }

        try
        {
            var requestPrepTime = DateTimeOffset.Now;
            // Prepare proposal request with minimal overhead
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = Math.Round(Math.Max(Stake, MinStakeAllowed), 2),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }

            var requestReadyTime = DateTimeOffset.Now;
            var prepDelay = (requestReadyTime - requestPrepTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Request preparado às {requestReadyTime:HH:mm:ss.fff} (prep delay: {prepDelay}ms)");

            // Use SendFastRequestAsync for ultra-low latency when in Fast Martingale mode
            var response = IsFastMartingale ? 
                await _derivApiService.GetFastProposalAsync(request) : 
                await _derivApiService.GetProposalAsync(request);
            
            var proposalReceivedTime = DateTimeOffset.Now;
            var proposalDelay = (proposalReceivedTime - requestReadyTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Proposta recebida às {proposalReceivedTime:HH:mm:ss.fff} (API delay: {proposalDelay}ms)");

            if (response.Error == null && response.Proposal != null)
            {
                var uiUpdateStartTime = DateTimeOffset.Now;
                // Update UI with proposal data (minimal assignments)
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;

                var uiUpdateEndTime = DateTimeOffset.Now;
                var uiUpdateDelay = (uiUpdateEndTime - uiUpdateStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] UI atualizada às {uiUpdateEndTime:HH:mm:ss.fff} (UI delay: {uiUpdateDelay}ms)");

                // Execute buy immediately without additional checks for maximum speed
                if (IsConnected && AskPrice > 0 && !string.IsNullOrEmpty(CurrentProposalId))
                {
                    var buyStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] Iniciando compra às {buyStartTime:HH:mm:ss.fff}. ProposalId: {CurrentProposalId}, Price: {AskPrice}");
                    await ExecuteBuyCommand();
                    
                    var buyEndTime = DateTimeOffset.Now;
                    var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                    var totalDelay = (buyEndTime - methodStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] Compra concluída às {buyEndTime:HH:mm:ss.fff} (buy delay: {buyDelay}ms, total: {totalDelay}ms)");
                }
                else
                {
                    _logger.LogInformation($"[TIMING] Compra cancelada - condições não atendidas. IsConnected: {IsConnected}, AskPrice: {AskPrice}, ProposalId: {CurrentProposalId}");
                }
            }
            else
            {
                _logger.LogInformation($"[TIMING] Proposta inválida recebida. Error: {response.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            var errorTime = DateTimeOffset.Now;
            var errorDelay = (errorTime - methodStartTime).TotalMilliseconds;
            _logger.LogError(ex, $"[TIMING] Erro em CalculateProposalAndBuyAsync às {errorTime:HH:mm:ss.fff} (após {errorDelay}ms)");
        }
    }

    private async void CalculateProposalAsync()
    {
        _logger.LogInformation("[DEBUG] CalculateProposalAsync chamado");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null || IsCalculating)
        {
            _logger.LogInformation($"[DEBUG] Saindo: SelectedContractType={SelectedContractType?.ContractType}, SelectedActiveSymbol={SelectedActiveSymbol?.Symbol}, IsCalculating={IsCalculating}");
            return;
        }

        // Verificar se todos os campos obrigatórios estão preenchidos
        if (IsBarrier1Visible && string.IsNullOrWhiteSpace(Barrier1Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible={IsBarrier1Visible}, Barrier1Value='{Barrier1Value}'");
            return;
        }
        if (IsBarrier2Visible && string.IsNullOrWhiteSpace(Barrier2Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier2 obrigatória mas vazia. IsBarrier2Visible={IsBarrier2Visible}, Barrier2Value='{Barrier2Value}'");
            return;
        }
        if (DurationValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationValue inválida: {DurationValue}");
            return;
        }
        if (string.IsNullOrWhiteSpace(DurationUnit))
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationUnit vazia: '{DurationUnit}'");
            return;
        }
        // Use a propriedade Stake diretamente em vez de TryGetStakeAmountDecimal para evitar inconsistências
        decimal stakeValue = Stake;
        if (stakeValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: Stake inválido: {stakeValue}");
            return;
        }

        _logger.LogInformation($"[DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType={SelectedContractType.ContractType}, Symbol={SelectedActiveSymbol.Symbol}, Stake={stakeValue}");

        try
        {
            IsCalculating = true;

            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = Math.Round(Math.Max(stakeValue, MinStakeAllowed), 2),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };
            
            // Só incluir LastDigitPrediction se for necessário para este tipo de contrato
            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            // Não definir LastDigitPrediction quando não necessário - deixar como não definido

            var response = await _derivApiService.GetProposalAsync(request);

            if (response.Error != null)
            {
                _logger.LogWarning($"Erro na proposta: {response.Error.Message}");
                CalculatedPayout = 0;
                AskPrice = 0;
                CalculatedBarrier1 = string.Empty;
                CalculatedBarrier2 = string.Empty;
            }
            else if (response.Proposal != null)
            {
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao calcular proposta");
            CalculatedPayout = 0;
            AskPrice = 0;
            CalculatedBarrier1 = string.Empty;
            CalculatedBarrier2 = string.Empty;
        }
        finally
        {
            IsCalculating = false;
        }
    }

    // Comando para compra
    private ICommand? _buyCommand;
    public ICommand BuyCommand
    {
        get
        {
            return _buyCommand ??= new RelayCommand(async () => await ExecuteBuyCommand(), CanExecuteBuy);
        }
    }

    // Comando para parar (SOLID: Single Responsibility)
    private ICommand? _stopCommand;
    public ICommand StopCommand
    {
        get
        {
            return _stopCommand ??= new RelayCommand(ExecuteStopCommand, CanExecuteStop);
        }
    }

    private bool CanExecuteBuy()
    {
        _logger.LogInformation($"[DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: {Environment.StackTrace.Split('\n')[1].Trim()})");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] IsConnected: {IsConnected}");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] IsTradingEnabled: {IsTradingEnabled}");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] _userClickedStop: {_userClickedStop}");
        
        // Permite trading se estiver habilitado OU se o usuário clicou em Stop (para reativar)
        // CORREÇÃO: Sempre permitir reativação se não há stop loss ativo
        var tradingAllowed = IsTradingEnabled || _userClickedStop || (!IsTradingEnabled && IsConnected);
        _logger.LogInformation($"[DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): {tradingAllowed}");
        
        // Verificar Max Loss Amount - parar entradas se prejuízo atingido
        decimal maxLossToCheck = IsDualEnabled ? DualMaxLossAmount : MaxLossAmount;
        // No modo dual, medir o prejuízo pelo Profit/Sessão; nos demais, por TotalProfit
        var lossGauge = IsDualEnabled ? SessionProfit : TotalProfit;
        if (maxLossToCheck > 0 && lossGauge <= -maxLossToCheck)
        {
            string mode = IsDualEnabled ? "Dual" : "Martingale";
            _logger.LogWarning($"[STOP LOSS] Trading bloqueado - Prejuízo atingido no modo {mode}: {lossGauge:F2} <= -{maxLossToCheck:F2}");

            if (IsTradingEnabled)
            {
                IsTradingEnabled = false;
                _logger.LogWarning($"[STOP LOSS] Trading automaticamente desabilitado por atingir Max Loss Amount");

                // Exibir mensagem "Stop Loss" para o usuário
                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Stop Loss.\n\nPrejuízo máximo atingido: {lossGauge:F2}\nLimite configurado: -{maxLossToCheck:F2}",
                                    "Stop Loss Ativado",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Warning);
                });
            }
            return false;
        }
        
        _logger.LogInformation($"[DEBUG CanExecuteBuy] SelectedActiveSymbol: {SelectedActiveSymbol?.Symbol ?? "null"}");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] SelectedContractType: {SelectedContractType?.ContractType ?? "null"}");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] Stake: {Stake}");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] DurationValue: {DurationValue}");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] DurationUnit: '{DurationUnit}'");
        _logger.LogInformation($"[DEBUG CanExecuteBuy] IsDualEnabled: {IsDualEnabled}");
        if (IsDualEnabled)
            _logger.LogInformation($"[DEBUG CanExecuteBuy] SessionProfit: {SessionProfit:F2}, DualMaxLossAmount: {DualMaxLossAmount:F2}");
        else
            _logger.LogInformation($"[DEBUG CanExecuteBuy] TotalProfit: {TotalProfit:F2}, MaxLossAmount: {MaxLossAmount:F2}");
        
        var baseValidation = IsConnected && 
                            tradingAllowed &&
                            SelectedActiveSymbol != null && 
                            SelectedContractType != null && 
                            Stake > 0 && 
                            DurationValue > 0 && 
                            !string.IsNullOrEmpty(DurationUnit);
        
        _logger.LogInformation($"[DEBUG CanExecuteBuy] BaseValidation: {baseValidation}");
        
        if (IsDualEnabled)
        {
            _logger.LogInformation($"[DEBUG CanExecuteBuy] SelectedDualContractType: {SelectedDualContractType?.ContractType ?? "null"}");
            _logger.LogInformation($"[DEBUG CanExecuteBuy] _isDualEntryPending: {_isDualEntryPending}");
            _logger.LogInformation($"[DEBUG CanExecuteBuy] _pendingDualContracts.Count: {_pendingDualContracts.Count}");
            _logger.LogInformation($"[DEBUG CanExecuteBuy] IsDualAutoMode: {IsDualAutoMode}");

            // CORREÇÃO: Para modo dual, verificar se pode executar baseado no modo
            bool hasNoPendingEntry = !_isDualEntryPending && _pendingDualContracts.Count == 0;

            // Em modo manual, sempre permitir se não há entrada pendente
            // Em modo automático, usar a lógica anterior
            bool canExecuteDual = IsDualAutoMode ? hasNoPendingEntry : hasNoPendingEntry;

            var dualResult = baseValidation && SelectedDualContractType != null && canExecuteDual;
            _logger.LogInformation($"[DEBUG CanExecuteBuy] Dual Mode Result: {dualResult} (allowing automatic continuation)");
            return dualResult;
        }
        else
        {
            _logger.LogInformation($"[DEBUG CanExecuteBuy] AskPrice: {AskPrice}");
            _logger.LogInformation($"[DEBUG CanExecuteBuy] CurrentProposalId: '{CurrentProposalId ?? "null"}'");
            var normalResult = baseValidation && 
                   AskPrice > 0 &&
                   !string.IsNullOrEmpty(CurrentProposalId);
            _logger.LogInformation($"[DEBUG CanExecuteBuy] Normal Mode Result: {normalResult}");
            return normalResult;
        }
    }

    private bool CanExecuteStop()
    {
        return IsTradingEnabled; // Can stop only if trading is currently enabled
    }

    private void ExecuteStopCommand()
    {
        IsTradingEnabled = false;
        
        // Marcar que o usuário clicou em Stop para atualizar saldo inicial no próximo Buy
        _userClickedStop = true;
        _logger.LogInformation("[STOP] User clicked Stop - will update initial balance on next Buy");
        
        // Parar também o modo dual
        if (IsDualEnabled && _isDualEntryPending)
        {
            _logger.LogInformation("[STOP] Stopping dual mode execution");
            _isDualEntryPending = false;
            _pendingDualContracts.Clear();
            _completedDualContracts = 0;
        }
        
        // Limpar pool do Fast Martingale se estiver ativo
        if (IsFastMartingale)
        {
            _logger.LogInformation("[STOP] Clearing Fast Martingale hot pool due to Stop command");
            lock (_poolLock)
            {
                _hotProposalPool.Clear();
            }
        }
        
        // Registrar final da sessão para estatísticas multi-sessão
        RecordSessionEnd();
        
        _logger.LogInformation("STOP command executed - Trading disabled");
    }

    private async Task ExecuteBuyCommand()
    {
        try
        {
            _logger.LogInformation($"[BUY] ExecuteBuyCommand called - IsDualEnabled: {IsDualEnabled}");
            
            // Verificação de pausa automática removida
            
            // Reabilitar trading quando o usuário clica em Buy
            if (!IsTradingEnabled)
            {
                IsTradingEnabled = true;
                _logger.LogInformation("[BUY] Trading re-enabled after Stop->Buy sequence");
            }
            
            // Verificar se o usuário clicou em Stop antes - NÃO redefinir InitialBalance
            if (_userClickedStop)
            {
                // CORREÇÃO: NÃO redefinir InitialBalance aqui pois causa inconsistência
                // InitialBalance deve permanecer o valor original da sessão
                _userClickedStop = false;
                _logger.LogInformation("[BALANCE] Stop->Buy sequence detected. InitialBalance mantido em: {InitialBalance:F2}", InitialBalance);
                
                // Reset dual mode state if dual is enabled
                if (IsDualEnabled)
                {
                    ResetDualModeState();
                    _logger.LogInformation("[DUAL] Dual mode state reset after Stop->Buy sequence");
                }
                
                _logger.LogInformation($"[BALANCE] Initial balance updated to: {InitialBalance:F2} after Stop->Buy sequence");
            }
            
            // Verificar se é modo Dual
            if (IsDualEnabled)
            {
                _logger.LogInformation("[BUY] Dual mode detected, calling ExecuteDualEntryCommand");
                
                // Force verification before dual entry
                Console.WriteLine("[MANUAL VERIFICATION] Before dual entry:");
                VerifyTotalProfitCalculation();
                
                await ExecuteDualEntryCommand();
                return;
            }

            // Check if CurrentProposalId is not null before making the purchase
            if (CurrentProposalId == null)
            {
                _logger.LogError("Cannot buy contract: CurrentProposalId is null");
                return;
            }
            
            // Verificar timing otimizado antes da compra
            if (ShouldWaitForBetterTiming())
            {
                _logger.LogInformation("[TIMING] Operação adiada aguardando condições mais favoráveis");
                return;
            }

            // LOG DETALHADO: Estado antes da compra
            var balanceBeforeBuy = CalculateExpectedBalance();
            var activeExposureBeforeBuy = ActiveExposure;
            _logger.LogInformation($"[ENTRY] ===== INICIANDO COMPRA =====" );
            _logger.LogInformation($"[ENTRY] Saldo antes da compra: {balanceBeforeBuy:F2}");
            _logger.LogInformation($"[ENTRY] Active Exposure antes: {activeExposureBeforeBuy:F2}");
            _logger.LogInformation($"[ENTRY] Stake a ser debitado: {Stake:F2}");
            _logger.LogInformation($"[ENTRY] Proposal ID: {CurrentProposalId}");
            _logger.LogInformation($"[ENTRY] Ask Price: {AskPrice:F2}");
            
            // Realizar a compra usando a API da Deriv
            var buyResponse = await _derivApiService.BuyContractAsync(CurrentProposalId, AskPrice);

            if (buyResponse.Error != null)
            {
                _logger.LogError("[ENTRY] ERRO na compra: {ErrorMessage}", buyResponse.Error.Message);
                return;
            }

            if (buyResponse.Buy != null)
            {
                // LOG DETALHADO: Compra executada com sucesso
                _logger.LogInformation("[ENTRY] Compra executada com SUCESSO. ContractId: {ContractId}, TransactionId: {TransactionId}",
                                      buyResponse.Buy.ContractId, buyResponse.Buy.TransactionId);
                
                // LOG DETALHADO: Impacto imediato no saldo
                var balanceAfterBuy = CalculateExpectedBalance();
                var activeExposureAfterBuy = ActiveExposure;
                _logger.LogInformation($"[ENTRY] Saldo após compra: {balanceAfterBuy:F2}");
                _logger.LogInformation($"[ENTRY] Active Exposure após: {activeExposureAfterBuy:F2}");
                _logger.LogInformation($"[ENTRY] Diferença no saldo: {balanceAfterBuy - balanceBeforeBuy:F2}");
                _logger.LogInformation($"[ENTRY] Diferença no Active Exposure: {activeExposureAfterBuy - activeExposureBeforeBuy:F2}");

                // Add entry to Profit Table
                AddProfitTableEntry(buyResponse.Buy);

                // CRITICAL: IMMEDIATE SYNCHRONOUS HOT POOL POPULATION
                // Must populate pool NOW before any potential loss occurs
                if (IsMartingaleEnabled && IsFastMartingale)
                {
                    var preCalcStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às {preCalcStartTime:HH:mm:ss.fff}");

                    // SYNCHRONOUS population - block until complete to ensure proposals are ready
                    try
                    {
                        await PopulateHotProposalPoolImmediate();

                        var preCalcEndTime = DateTimeOffset.Now;
                        var preCalcDuration = (preCalcEndTime - preCalcStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em {preCalcDuration}ms - propostas GARANTIDAMENTE prontas");

                        lock (_poolLock)
                        {
                            _logger.LogInformation($"[DEBUG] HOT POOL STATUS: {_hotProposalPool.Count} propostas prontas nos níveis: [{string.Join(", ", _hotProposalPool.Keys)}]");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] IMMEDIATE SYNC HOT POOL: ERRO CRÍTICO no pré-cálculo síncrono");
                    }
                }

                // Recalcular proposta automaticamente para manter o botão pronto
                CalculateProposalAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar compra");
        }
    }

    // Métodos do Modo Dual
    private async Task ExecuteDualEntryCommand()
    {
        try
        {
            _logger.LogInformation($"[DUAL DEBUG] 🚀 ExecuteDualEntryCommand started");
            Console.WriteLine($"[DUAL DEBUG] 🚀 ExecuteDualEntryCommand started");

            // Verificar conexão antes de executar
            if (!IsConnected)
            {
                _logger.LogWarning("[DUAL] Cannot execute dual entry: not connected to API");
                Console.WriteLine("[DUAL DEBUG] Execution skipped due to disconnection");
                return;
            }

            _logger.LogInformation($"[DUAL DEBUG] Connection check passed");
            
            // Sem delay no modo dual: ignorar checagem de timing para executar imediatamente
            _logger.LogInformation($"[DUAL DEBUG] Timing check bypassed for dual mode (no-delay)");
            
            // CRITICAL: Enhanced validation for contract types after reconnection
            _logger.LogInformation($"[DUAL DEBUG] Validating contract types - ContractType: {SelectedContractType?.ContractType}, DualContractType: {SelectedDualContractType?.ContractType}, Symbol: {SelectedActiveSymbol?.Symbol}");

            if (SelectedContractType == null || SelectedDualContractType == null || SelectedActiveSymbol == null)
            {
                _logger.LogError("[DUAL] Cannot execute dual entry: missing contract types or symbol");
                Console.WriteLine($"[DUAL ERROR] Missing critical data - ContractType: {SelectedContractType?.ContractType}, DualContractType: {SelectedDualContractType?.ContractType}, Symbol: {SelectedActiveSymbol?.Symbol}");

                // Attempt to recover if we have symbol but missing contract types
                if (SelectedActiveSymbol != null && (SelectedContractType == null || SelectedDualContractType == null))
                {
                    _logger.LogWarning("[DUAL] Attempting emergency contract type recovery...");
                    Console.WriteLine("[DUAL RECOVERY] Attempting to reload contract types...");
                    
                    try
                    {
                        // Force reload contract types
                        var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedActiveSymbol.Symbol);
                        
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            ContractTypes.Clear();
                            DualContractTypes.Clear();
                            
                            foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
                            {
                                ContractTypes.Add(contract);
                                DualContractTypes.Add(contract);
                            }
                            
                            _logger.LogInformation($"[DUAL RECOVERY] Loaded {ContractTypes.Count} contract types");
                        });
                        
                        // If still missing after reload, abort
                        if (SelectedContractType == null || SelectedDualContractType == null)
                        {
                            _logger.LogError("[DUAL RECOVERY] Failed to restore contract types - aborting dual entry");
                            Console.WriteLine("[DUAL RECOVERY] FAILED - Contract types still missing after reload");
                            return;
                        }
                        else
                        {
                            _logger.LogInformation("[DUAL RECOVERY] Contract types successfully restored, proceeding with dual entry");
                            Console.WriteLine("[DUAL RECOVERY] SUCCESS - Contract types restored, continuing...");
                        }
                    }
                    catch (Exception recoveryEx)
                    {
                        _logger.LogError(recoveryEx, "[DUAL RECOVERY] Failed to recover contract types");
                        Console.WriteLine($"[DUAL RECOVERY] Recovery failed: {recoveryEx.Message}");
                        return;
                    }
                }
                else
                {
                    return;
                }
            }

            // Inicializar primeira sessão se necessário
            if (CurrentDualSession == 0)
            {
                CurrentDualSession = 1;
                _logger.LogInformation($"[DUAL] Iniciando primeira sessão dual - Session {CurrentDualSession}");
            }
            
            _logger.LogInformation($"[DUAL] Iniciando entrada dupla - Level {CurrentDualLevel}/{DualLevel}, Session {CurrentDualSession}/{DualSession}");

            // Calcular stakes para entrada dupla baseadas no payout
            var (stake1_calculated, stake2_calculated, useRandomChoice) = await CalculateDualStakesAsync();

            // ESTRATÉGIA DUAL: atribuir maior/menor baseado na lógica de perdas
            decimal higherStake = Math.Max(stake1_calculated, stake2_calculated);
            decimal lowerStake = Math.Min(stake1_calculated, stake2_calculated);
            
            // Determinar qual contrato recebe stake maior
            bool isFirstContractHigher;
            if (CurrentDualLevel == 0)
            {
                // Primeira entrada: escolha aleatória
                isFirstContractHigher = useRandomChoice;
                _logger.LogInformation($"[DUAL] Primeira entrada - Random choice: Contrato principal {(isFirstContractHigher ? "MAIOR" : "MENOR")} stake");
            }
            else
            {
                // Entradas subsequentes: stake maior vai para o contrato que perdeu na rodada anterior
                isFirstContractHigher = (_losingContractTypeIndex == 0);
                _logger.LogInformation($"[DUAL] Entrada subsequente - Stake MAIOR vai para: {(isFirstContractHigher ? "Contrato 1" : "Contrato 2")} (que perdeu na rodada anterior)");
            }

            decimal stake1 = isFirstContractHigher ? higherStake : lowerStake;
            decimal stake2 = isFirstContractHigher ? lowerStake : higherStake;
            // Proporção R (y/x) já foi aplicada em CalculateNewDualStakes()
            // Não aplicar a proporção fixa de 80% aqui para não distorcer R.
            
            _logger.LogInformation($"[DUAL] Stakes atribuídas - {SelectedContractType?.ContractDisplay}: {stake1:F2}, {SelectedDualContractType?.ContractDisplay}: {stake2:F2}");
            _logger.LogInformation($"[DUAL] Stake MAIOR ({Math.Max(stake1, stake2):F2}) vai para: {(stake1 > stake2 ? SelectedContractType?.ContractDisplay : SelectedDualContractType?.ContractDisplay)}");
            
            // Armazenar qual stake é maior para controle futuro
            _isFirstContractHigherStake = stake1 > stake2;
            
            _logger.LogInformation($"[DUAL] Stakes finais - {SelectedContractType?.ContractDisplay}: {stake1:F2}, {SelectedDualContractType?.ContractDisplay}: {stake2:F2}");

            // Verificar se os contratos estão selecionados
            if (SelectedContractType == null || SelectedDualContractType == null)
            {
                _logger.LogError("[DUAL] Contract types not selected, cannot proceed with dual entry");
                return;
            }

            // Obter propostas para ambos os contratos com stakes específicas
            var proposal1 = await GetProposalForContract(SelectedContractType, stake1);
            var proposal2 = await GetProposalForContract(SelectedDualContractType, stake2);

            if (proposal1 == null || proposal2 == null)
            {
                _logger.LogError("[DUAL] Failed to get proposals for dual contracts, retrying in 2 seconds...");
                
                // Reset dual state and retry immediately
                _isDualEntryPending = false;
                
                _ = Task.Run(() =>
                {
                    if (IsDualEnabled && IsTradingEnabled)
                    {
                        _logger.LogInformation("[DUAL] Retrying dual entry after proposal timeout");
                        _ = Task.Run(async () => await CalculateDualStakesAsync());
                    }
                });
                
                return;
            }

            if (proposal1.Proposal == null || proposal2.Proposal == null)
            {
                _logger.LogError("[DUAL] One or both proposals are null");
                return;
            }

            _logger.LogInformation($"[DUAL] Payouts - Contrato 1 ({SelectedContractType.ContractDisplay}): {proposal1.Proposal.Payout:F2}, Contrato 2 ({SelectedDualContractType.ContractDisplay}): {proposal2.Proposal.Payout:F2}");

            // Armazenar informações para controle
            _dualStakeHigher = Math.Max(stake1, stake2);
            _dualStakeLower = Math.Min(stake1, stake2);
            
            _isDualEntryPending = true;
            _pendingDualContracts.Clear();
            _completedDualContracts = 0;
            
            // Armazenar stakes específicas para cálculo dual
            _dualContract1Stake = stake1;
            _dualContract2Stake = stake2;
            _dualContract1Profit = 0;
            _dualContract2Profit = 0;
            _dualContract1Completed = false;
            _dualContract2Completed = false;

            // Verificar se as propostas são válidas
            if (proposal1?.Proposal?.Id == null || proposal2?.Proposal?.Id == null)
            {
                _logger.LogError("[DUAL] Invalid proposals received, cannot execute buy commands");
                _isDualEntryPending = false;
                return;
            }

            // LOG DETALHADO: Estado antes das compras duais
            var balanceBeforeDualBuy = CalculateExpectedBalance();
            var activeExposureBeforeDualBuy = ActiveExposure;
            var totalStakeDual = stake1 + stake2;
            
            _logger.LogInformation($"[DUAL ENTRY] ===== INICIANDO COMPRAS DUAIS =====");
            _logger.LogInformation($"[DUAL ENTRY] Saldo antes das compras: {balanceBeforeDualBuy:F2}");
            _logger.LogInformation($"[DUAL ENTRY] Active Exposure antes: {activeExposureBeforeDualBuy:F2}");
            _logger.LogInformation($"[DUAL ENTRY] Total stake a ser debitado: {totalStakeDual:F2}");
            
            // Executar compras simultâneas
            _logger.LogInformation($"[DUAL] INTENT BUY -> C1={SelectedContractType.ContractDisplay}, Stake={stake1:F2}, ProposalId={proposal1.Proposal.Id}");
            _logger.LogInformation($"[DUAL] INTENT BUY -> C2={SelectedDualContractType.ContractDisplay}, Stake={stake2:F2}, ProposalId={proposal2.Proposal.Id}");

            var buyTask1 = _derivApiService.BuyContractAsync(proposal1.Proposal.Id, proposal1.Proposal.AskPrice);
            var buyTask2 = _derivApiService.BuyContractAsync(proposal2.Proposal.Id, proposal2.Proposal.AskPrice);

            var buyResults = await Task.WhenAll(buyTask1, buyTask2);

            // Processar resultados
            bool contract1Success = buyResults[0].Error == null && buyResults[0].Buy != null;
            bool contract2Success = buyResults[1].Error == null && buyResults[1].Buy != null;

            if (contract1Success && contract2Success)
            {
                _logger.LogInformation("[DUAL] Ambas as compras executadas com sucesso");
                
                // LOG DETALHADO: Impacto das compras duais no saldo
                var balanceAfterDualBuy = CalculateExpectedBalance();
                var activeExposureAfterDualBuy = ActiveExposure;
                _logger.LogInformation($"[DUAL ENTRY] Saldo após compras: {balanceAfterDualBuy:F2}");
                _logger.LogInformation($"[DUAL ENTRY] Active Exposure após: {activeExposureAfterDualBuy:F2}");
                _logger.LogInformation($"[DUAL ENTRY] Diferença no saldo: {balanceAfterDualBuy - balanceBeforeDualBuy:F2}");
                _logger.LogInformation($"[DUAL ENTRY] Diferença no Active Exposure: {activeExposureAfterDualBuy - activeExposureBeforeDualBuy:F2}");
                _logger.LogInformation($"[DUAL ENTRY] Contract 1 ID: {buyResults[0].Buy!.ContractId}");
                _logger.LogInformation($"[DUAL ENTRY] Contract 2 ID: {buyResults[1].Buy!.ContractId}");

                // Adicionar entradas individuais à tabela de profit
                AddProfitTableEntry(buyResults[0].Buy!, SelectedContractType.ContractDisplay);
                AddProfitTableEntry(buyResults[1].Buy!, SelectedDualContractType.ContractDisplay);

                // Registrar contratos pendentes para monitoramento
                _pendingDualContracts.Add(buyResults[0].Buy!.ContractId.ToString());
                _pendingDualContracts.Add(buyResults[1].Buy!.ContractId.ToString());

                // Não incrementar nível após compra - apenas após resultado de derrota
                _logger.LogInformation($"[DUAL] Compra dupla executada no nível {CurrentDualLevel} (máximo: {DualLevel})");
            }
            else
            {
                _logger.LogError("[DUAL] Falha em uma ou ambas as compras");
                _isDualEntryPending = false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL] Erro ao executar entrada dupla");
            
            // SISTEMA DE RECUPERAÇÃO ROBUSTO APÓS TIMEOUT
            if (ex is TimeoutException || ex.Message.Contains("timeout") || ex.Message.Contains("expirou"))
            {
                await HandleDualRecoveryAsync(ex);
            }
            else if (ex.Message.Contains("connection") || ex.Message.Contains("network") || ex.Message.Contains("websocket"))
            {
                // Erros de conexão também são recuperáveis
                await HandleDualRecoveryAsync(ex);
            }
            else
            {
                // Para outros erros, parar dual mode
                _isDualEntryPending = false;
                _dualRecoveryAttempts = 0; // Reset contador de tentativas
                _logger.LogError("[DUAL] Erro não recuperável - dual mode interrompido");
            }
        }
    }

    /// <summary>
    /// Método robusto para recuperação do DUAL RECOVERY com múltiplas tentativas e backoff exponencial
    /// </summary>
    private async Task HandleDualRecoveryAsync(Exception originalException)
    {
        try
        {
            _dualRecoveryAttempts++;
            var timeSinceLastAttempt = DateTime.Now - _lastRecoveryAttempt;
            var recoveryStartTime = DateTime.Now; // Adicionar variável para tracking de tempo
            
            _logger.LogWarning($"[DUAL RECOVERY] Tentativa {_dualRecoveryAttempts}/{_maxRecoveryAttempts} - Erro: {originalException.Message}");
            Console.WriteLine($"[DUAL RECOVERY] Iniciando recuperação - Tentativa {_dualRecoveryAttempts}");
            
            // Verificar se excedeu o número máximo de tentativas
            if (_dualRecoveryAttempts > _maxRecoveryAttempts)
            {
                _logger.LogError($"[DUAL RECOVERY] Máximo de tentativas excedido ({_maxRecoveryAttempts}) - Interrompendo dual mode");
                Console.WriteLine("[DUAL RECOVERY] FALHA - Máximo de tentativas excedido");
                
                _isDualEntryPending = false;
                _dualRecoveryAttempts = 0;
                IsDualEnabled = false;
                return;
            }
            
            // Calcular delay com backoff exponencial (5s, 10s, 20s)
            var baseDelay = _recoveryDelayMs * Math.Pow(2, _dualRecoveryAttempts - 1);
            var delayMs = Math.Min((int)baseDelay, 30000); // Máximo 30 segundos
            
            _logger.LogInformation($"[DUAL RECOVERY] Aguardando {delayMs/1000}s antes da próxima tentativa (backoff exponencial)");
            Console.WriteLine($"[DUAL RECOVERY] Delay de {delayMs/1000}s aplicado");
            
            _lastRecoveryAttempt = DateTime.Now;
            
            // Executar recuperação em background
            _ = Task.Run(async () =>
            {
                await Task.Delay(delayMs);
                
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        // Verificar condições antes de tentar recuperação
                        if (!IsDualEnabled)
                        {
                            _logger.LogWarning("[DUAL RECOVERY] Dual mode foi desabilitado durante recuperação");
                            _dualRecoveryAttempts = 0;
                            return;
                        }
                        
                        if (!IsConnected)
                         {
                             _logger.LogWarning("[DUAL RECOVERY] Sem conexão - aguardando reconexão");
                             _logger.LogInformation($"[DUAL RECOVERY DEBUG] Connection Status: IsConnected={IsConnected}, Ping={Ping}ms, AccountCode={AccountCode}");
                             Console.WriteLine($"[DUAL RECOVERY] Aguardando reconexão... (Ping: {Ping}ms, Account: {AccountCode})");
                             return; // Não resetar tentativas, aguardar reconexão
                         }
                        
                        if (!IsTradingEnabled)
                        {
                            _logger.LogWarning("[DUAL RECOVERY] Trading desabilitado durante recuperação");
                            _dualRecoveryAttempts = 0;
                            return;
                        }
                        
                        // Validar dados críticos
                         if (SelectedContractType == null || SelectedDualContractType == null || SelectedActiveSymbol == null)
                         {
                             _logger.LogError("[DUAL RECOVERY] Dados críticos ausentes - tentando recarregar");
                             _logger.LogInformation($"[DUAL RECOVERY DEBUG] Missing Data - ContractType: {SelectedContractType?.ContractType ?? "NULL"}, DualContractType: {SelectedDualContractType?.ContractType ?? "NULL"}, Symbol: {SelectedActiveSymbol?.Symbol ?? "NULL"}");
                             _logger.LogInformation($"[DUAL RECOVERY DEBUG] Available Contract Types Count: {ContractTypes?.Count ?? 0}, Active Symbols Count: {ActiveSymbols?.Count ?? 0}");
                             Console.WriteLine($"[DUAL RECOVERY] Recarregando dados críticos... (Contracts: {ContractTypes?.Count ?? 0}, Symbols: {ActiveSymbols?.Count ?? 0})");
                            
                            // Tentar recarregar contract types
                             PopulateDualContractTypes();
                             await Task.Delay(1000);
                            
                            if (SelectedContractType == null || SelectedDualContractType == null)
                             {
                                 _logger.LogError("[DUAL RECOVERY] Falha ao recarregar dados - tentativa falhada");
                                 _logger.LogError($"[DUAL RECOVERY DEBUG] Reload Failed - ContractType: {SelectedContractType?.ContractType ?? "STILL NULL"}, DualContractType: {SelectedDualContractType?.ContractType ?? "STILL NULL"}");
                                 _logger.LogError($"[DUAL RECOVERY DEBUG] Post-Reload Counts - Contract Types: {ContractTypes?.Count ?? 0}, Active Symbols: {ActiveSymbols?.Count ?? 0}");
                                 Console.WriteLine($"[DUAL RECOVERY] FALHA no reload - Contracts ainda NULL (Available: {ContractTypes?.Count ?? 0})");
                                 return; // Tentar novamente na próxima iteração
                             }
                        }
                        
                        _logger.LogInformation($"[DUAL RECOVERY] Executando tentativa {_dualRecoveryAttempts} - Level {CurrentDualLevel}");
                         _logger.LogInformation($"[DUAL RECOVERY DEBUG] Pre-Execution State - IsDualEnabled: {IsDualEnabled}, IsTradingEnabled: {IsTradingEnabled}, IsConnected: {IsConnected}");
                         _logger.LogInformation($"[DUAL RECOVERY DEBUG] Contract State - Main: {SelectedContractType?.ContractType}, Dual: {SelectedDualContractType?.ContractType}, Symbol: {SelectedActiveSymbol?.Symbol}");
                         _logger.LogInformation($"[DUAL RECOVERY DEBUG] Dual State - Session: {CurrentDualSession}/{DualSession}, Level: {CurrentDualLevel}/{DualLevel}, AccumulatedLoss: {_accumulatedLoss:F2}");
                         Console.WriteLine($"[DUAL RECOVERY] Executando recuperação para nível {CurrentDualLevel} (Session {CurrentDualSession}/{DualSession})");
                        
                        // Reset do estado de pending antes da nova tentativa
                        _isDualEntryPending = false;
                        
                        // Executar nova tentativa
                        await ExecuteDualEntryCommand();
                        
                        // Se chegou até aqui sem exceção, recuperação bem-sucedida
                        _logger.LogInformation($"[DUAL RECOVERY] ✓ Recuperação bem-sucedida na tentativa {_dualRecoveryAttempts}");
                        _logger.LogInformation($"[DUAL RECOVERY DEBUG] Success Details - Total Recovery Time: {DateTime.Now - recoveryStartTime:mm\\:ss\\.fff}, Final State: Level {CurrentDualLevel}, Session {CurrentDualSession}");
                        _logger.LogInformation($"[DUAL RECOVERY DEBUG] Post-Recovery State - Balance: {Balance:F2}, IsConnected: {IsConnected}, Ping: {Ping}ms");
                        Console.WriteLine($"[DUAL RECOVERY] SUCCESS - Recuperado na tentativa {_dualRecoveryAttempts} em {DateTime.Now - recoveryStartTime:mm\\:ss}");
                        _dualRecoveryAttempts = 0; // Reset contador após sucesso
                    }
                    catch (TimeoutException timeoutEx)
                    {
                        _logger.LogWarning($"[DUAL RECOVERY] Timeout na tentativa {_dualRecoveryAttempts}: {timeoutEx.Message}");
                        _logger.LogWarning($"[DUAL RECOVERY DEBUG] Timeout Details - Elapsed Time: {DateTime.Now - recoveryStartTime:mm\\:ss\\.fff}, Connection: {IsConnected}, Ping: {Ping}ms");
                        _logger.LogWarning($"[DUAL RECOVERY DEBUG] Timeout Context - Level: {CurrentDualLevel}, Session: {CurrentDualSession}, Balance: {Balance:F2}");
                        Console.WriteLine($"[DUAL RECOVERY] Timeout na tentativa {_dualRecoveryAttempts} (Ping: {Ping}ms)");
                        
                        // Para timeout, tentar novamente
                        await HandleDualRecoveryAsync(timeoutEx);
                    }
                    catch (Exception recoveryEx)
                    {
                        _logger.LogError(recoveryEx, $"[DUAL RECOVERY] Erro na tentativa {_dualRecoveryAttempts}");
                        _logger.LogError($"[DUAL RECOVERY DEBUG] Exception Details - Type: {recoveryEx.GetType().Name}, InnerException: {recoveryEx.InnerException?.Message ?? "None"}");
                        _logger.LogError($"[DUAL RECOVERY DEBUG] Error Context - Elapsed Time: {DateTime.Now - recoveryStartTime:mm\\:ss\\.fff}, IsConnected: {IsConnected}, Balance: {Balance:F2}");
                        _logger.LogError($"[DUAL RECOVERY DEBUG] Current State - Level: {CurrentDualLevel}, Session: {CurrentDualSession}, IsTradingEnabled: {IsTradingEnabled}");
                        Console.WriteLine($"[DUAL RECOVERY] Erro na tentativa {_dualRecoveryAttempts} ({recoveryEx.GetType().Name}): {recoveryEx.Message}");
                        
                        // Verificar se é erro recuperável
                        if (IsRecoverableError(recoveryEx))
                        {
                            _logger.LogInformation($"[DUAL RECOVERY DEBUG] Error is recoverable - continuing recovery process");
                            await HandleDualRecoveryAsync(recoveryEx);
                        }
                        else
                        {
                            _logger.LogError("[DUAL RECOVERY] Erro não recuperável - interrompendo dual mode");
                            _logger.LogError($"[DUAL RECOVERY DEBUG] Non-Recoverable Error - Stopping dual mode completely");
                            _logger.LogError($"[DUAL RECOVERY DEBUG] Final State - Total Attempts: {_dualRecoveryAttempts}, Total Time: {DateTime.Now - recoveryStartTime:mm\\:ss\\.fff}");
                            Console.WriteLine($"[DUAL RECOVERY] Erro não recuperável ({recoveryEx.GetType().Name}) - parando dual mode");
                            _isDualEntryPending = false;
                            _dualRecoveryAttempts = 0;
                            IsDualEnabled = false;
                        }
                    }
                });
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL RECOVERY] Erro crítico no sistema de recuperação");
            _logger.LogError($"[DUAL RECOVERY DEBUG] Critical Error Details - Type: {ex.GetType().Name}, Message: {ex.Message}");
            _logger.LogError($"[DUAL RECOVERY DEBUG] Critical Error Context - IsConnected: {IsConnected}, IsTradingEnabled: {IsTradingEnabled}, Balance: {Balance:F2}");
            _logger.LogError($"[DUAL RECOVERY DEBUG] System State - DualEnabled: {IsDualEnabled}, Level: {CurrentDualLevel}, Session: {CurrentDualSession}");
            _logger.LogError($"[DUAL RECOVERY DEBUG] Stack Trace: {ex.StackTrace}");
            Console.WriteLine($"[DUAL RECOVERY] ERRO CRÍTICO ({ex.GetType().Name}): {ex.Message}");
            _isDualEntryPending = false;
            _dualRecoveryAttempts = 0;
        }

        await Task.CompletedTask;
    }
    
    /// <summary>
    /// Verifica se um erro é recuperável para o sistema de DUAL RECOVERY
    /// </summary>
    private bool IsRecoverableError(Exception ex)
    {
        _logger.LogInformation($"[DUAL RECOVERY DEBUG] Analyzing error recoverability - Type: {ex.GetType().Name}, Message: {ex.Message}");
        
        // Erros recuperáveis: timeout, conexão, API temporariamente indisponível
        bool isRecoverable = ex is TimeoutException ||
               ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("connection", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("network", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("temporarily unavailable", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase);
               
        _logger.LogInformation($"[DUAL RECOVERY DEBUG] Error recoverability result: {isRecoverable}");
        
        if (!isRecoverable)
        {
            _logger.LogWarning($"[DUAL RECOVERY DEBUG] Non-recoverable error detected - Type: {ex.GetType().Name}, will stop recovery");
        }
        
        return isRecoverable;
    }

        private async Task<(decimal higherStake, decimal lowerStake, bool useRandomChoice)> CalculateDualStakesAsync()
    {
        try
        {
            // Usar as novas fórmulas matemáticas para calcular stakes
            _logger.LogInformation($"[NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...");
            Console.WriteLine($"[NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...");
            Console.WriteLine($"[NEW_DUAL] 📊 ANTES DA FÓRMULA - Alfa: {DualAlfa:F2}, Perdas: {DualPerdasAcumuladas:F2}, Base: {DualLucroBase:F2}, R(y/x): {DualP:F2}");

            var (stakeX, stakeY) = CalculateNewDualStakes();

            Console.WriteLine($"[NEW_DUAL] 📊 DEPOIS DA FÓRMULA - X: {stakeX:F2}, Y: {stakeY:F2}");

            _logger.LogInformation($"[NEW_DUAL] ✅ Calculated stakes using new formulas - X: {stakeX:F2}, Y: {stakeY:F2}");
            _logger.LogInformation($"[NEW_DUAL] 📊 Parameters - Lucro Alvo: {DualLucroAlvo:F2}, Alfa: {DualAlfa:F2}, Lucro Base: {DualLucroBase:F2}, R(y/x): {DualP:F2}");
            _logger.LogInformation($"[NEW_DUAL] 💰 Perdas Acumuladas: {DualPerdasAcumuladas:F2}");
            Console.WriteLine($"[NEW_DUAL] ✅ Calculated stakes using new formulas - X: {stakeX:F2}, Y: {stakeY:F2}");
            Console.WriteLine($"[NEW_DUAL] 📊 Parameters - Lucro Alvo: {DualLucroAlvo:F2}, Alfa: {DualAlfa:F2}, Lucro Base: {DualLucroBase:F2}, R(y/x): {DualP:F2}");
            Console.WriteLine($"[NEW_DUAL] 💰 Perdas Acumuladas: {DualPerdasAcumuladas:F2}");

            // Obter propostas para verificar payouts e determinar distribuição
            var tempProposal1 = await GetProposalForContract(SelectedContractType!, Math.Max(stakeX, MinStakeAllowed));
            var tempProposal2 = await GetProposalForContract(SelectedDualContractType!, Math.Max(stakeY, MinStakeAllowed));

            if (tempProposal1?.Proposal == null || tempProposal2?.Proposal == null)
            {
                _logger.LogWarning("[NEW_DUAL] Could not get proposals for payout verification, using calculated stakes");
                return (stakeX, stakeY, false);
            }

            decimal payout1 = tempProposal1.Proposal.Payout;
            decimal payout2 = tempProposal2.Proposal.Payout;

            _logger.LogInformation($"[NEW_DUAL] Payouts - Contrato 1: {payout1:F2}, Contrato 2: {payout2:F2}");

            // Determinar qual contrato recebe qual stake baseado nos payouts
            // X vai para o contrato A, Y vai para o contrato B
            bool contrato1TemMelhorPayout = payout1 >= payout2;

            // Distribuir stakes X e Y entre os contratos
            decimal stake1, stake2;
            if (contrato1TemMelhorPayout)
            {
                stake1 = stakeX;   // Contrato 1 (melhor payout) recebe stake X
                stake2 = stakeY;   // Contrato 2 recebe stake Y
            }
            else
            {
                stake1 = stakeY;   // Contrato 1 recebe stake Y
                stake2 = stakeX;   // Contrato 2 (melhor payout) recebe stake X
            }

            _logger.LogInformation($"[NEW_DUAL] Final stakes - Contrato 1: {stake1:F2}, Contrato 2: {stake2:F2}");

            return (stake1, stake2, false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[NEW_DUAL] Erro ao calcular stakes, usando valores mínimos");
            return (MinStakeAllowed, MinStakeAllowed, false);
        }
    }



    /// <summary>
    /// Calcula stakes balanceadas conforme especificação: primeira entrada considera payout, stake base e take profit.
    /// Entradas subsequentes: +30% na stake que perdeu, -20% na stake que ganhou, respeitando mínimo do campo Stake.
    /// </summary>
    // Campos para tracking do modo Balanced


    /// <summary>
    /// Calcula fator de ajuste das stakes baseado na variação do SessionProfit
    /// REGRA PRINCIPAL: Quando prejuízo diminui (SessionProfit fica menos negativo), stakes diminuem
    /// Quando prejuízo aumenta (SessionProfit fica mais negativo), stakes aumentam
    /// </summary>
    /// <summary>
    /// Calcula um fator de recuperação adaptativo baseado na performance recente
    /// </summary>
    private decimal CalculateAdaptiveRecoveryFactor(decimal baseStake)
    {
        decimal recoveryFactor = 1.0m;
        
        if (SessionProfit >= 0)
        {
            return recoveryFactor; // Sem recuperação necessária
        }
        
        // Calcular taxa de vitórias recentes (últimas 10 operações)
        decimal recentWinRate = CalculateRecentWinRate();
        
        // Calcular razão de perda normalizada
        decimal lossRatio = Math.Abs(SessionProfit) / Math.Max(baseStake * 8, 1);
        
        // Fator base de recuperação mais conservador
        decimal baseFactor = Math.Min(lossRatio * 0.3m, 0.6m); // Máximo 60% de aumento
        
        // Ajustar baseado na performance recente
        if (recentWinRate >= 0.6m) // Boa performance recente
        {
            recoveryFactor = 1.0m + baseFactor; // Recuperação normal
        }
        else if (recentWinRate >= 0.4m) // Performance média
        {
            recoveryFactor = 1.0m + (baseFactor * 0.7m); // Recuperação reduzida
        }
        else // Performance ruim
        {
            recoveryFactor = 1.0m + (baseFactor * 0.5m); // Recuperação muito conservadora
        }
        
        // Aplicar ajuste baseado na volatilidade do mercado
        decimal volatilityFactor = CalculateVolatilityAdjustmentFactor();
        recoveryFactor *= volatilityFactor;
        
        // Atualizar tracking de drawdown
        UpdateDrawdownTracking();
        
        // Aplicar proteção de drawdown
        decimal drawdownFactor = CalculateDrawdownProtectionFactor();
        recoveryFactor *= drawdownFactor;
        
        // Aplicar fator de recuperação multi-sessão
        recoveryFactor *= MultiSessionRecoveryFactor;
        
        // Limitar o fator máximo baseado no nível atual
        decimal maxFactor = 1.3m + (CurrentDualLevel * 0.05m); // Cresce gradualmente
        recoveryFactor = Math.Min(recoveryFactor, maxFactor);
        
        _logger.LogInformation($"[RECOVERY ADAPTATIVO] WinRate: {recentWinRate:P1}, LossRatio: {lossRatio:F3}, Volatilidade: {volatilityFactor:F3}, Drawdown: {drawdownFactor:F3}, Multi-Sessão: {MultiSessionRecoveryFactor:F3}, Fator Final: {recoveryFactor:F3}");
        
        return recoveryFactor;
    }
    
    /// <summary>
    /// Calcula a taxa de vitórias das últimas operações
    /// </summary>
    private decimal CalculateRecentWinRate()
     {
         if (ProfitTableEntries == null || ProfitTableEntries.Count < 3)
         {
             return 0.5m; // Assumir 50% se não há histórico suficiente
         }
         
         // Pegar as últimas 10 operações
         var recentEntries = ProfitTableEntries.TakeLast(Math.Min(10, ProfitTableEntries.Count)).ToList();
        
        if (recentEntries.Count == 0)
        {
            return 0.5m;
        }
        
        int wins = recentEntries.Count(entry => entry.TotalProfitLoss > 0);
        decimal winRate = (decimal)wins / recentEntries.Count;
        
        _logger.LogInformation($"[WIN RATE] Últimas {recentEntries.Count} operações: {wins} vitórias = {winRate:P1}");
        
        return winRate;
     }
     
     /// <summary>
     /// Aplica sistema de desaceleração quando stakes ficam muito altas
     /// </summary>
     private decimal ApplyStakeDeceleration(decimal currentStake, decimal baseStake)
     {
         // Calcular ratio da stake atual em relação à base
         decimal stakeRatio = currentStake / baseStake;
         
         // Aplicar desaceleração progressiva quando ratio fica muito alto
         if (stakeRatio > 8.0m) // Muito alta
         {
             decimal excessRatio = stakeRatio - 8.0m;
             decimal decelerationFactor = 1.0m - Math.Min(excessRatio * 0.1m, 0.4m); // Máximo 40% de redução
             currentStake *= decelerationFactor;
             
             _logger.LogWarning($"[DESACELERAÇÃO] Stake muito alta detectada. Ratio: {stakeRatio:F2}, Fator: {decelerationFactor:F3}");
         }
         else if (stakeRatio > 5.0m) // Moderadamente alta
         {
             decimal excessRatio = stakeRatio - 5.0m;
             decimal decelerationFactor = 1.0m - Math.Min(excessRatio * 0.05m, 0.2m); // Máximo 20% de redução
             currentStake *= decelerationFactor;
             
             _logger.LogInformation($"[DESACELERAÇÃO] Stake alta detectada. Ratio: {stakeRatio:F2}, Fator: {decelerationFactor:F3}");
         }
         
         return Math.Round(currentStake, 2);
     }
 
     private decimal CalculateSessionProfitAdjustmentFactor()
    {
        // Se não há histórico de SessionProfit anterior, usar fator neutro
        if (CurrentDualLevel <= 1)
        {
            return 1.0m;
        }

        decimal currentSessionProfit = SessionProfit;
        decimal previousSessionProfit = _previousSessionProfit;
        
        // Calcular variação do SessionProfit
        decimal profitVariation = currentSessionProfit - previousSessionProfit;
        
        // Fator base: 1.0 = sem ajuste
        decimal adjustmentFactor = 1.0m;
        
        // LÓGICA PRINCIPAL: Baseada na VARIAÇÃO do SessionProfit
        if (currentSessionProfit < 0) // Estamos em prejuízo
        {
            if (profitVariation > 0) // Prejuízo DIMINUIU (SessionProfit ficou menos negativo)
            {
                // Exemplo: era -0.40, agora é -0.38 -> diminuir stakes (reduzir risco)
                decimal improvementRatio = Math.Min(Math.Abs(profitVariation) / Math.Abs(previousSessionProfit), 0.5m);
                adjustmentFactor = Math.Max(0.85m, 1.0m - improvementRatio * 0.15m); // Reduzir até 15%
                _logger.LogInformation($"[ADJUSTMENT] Prejuízo DIMINUIU: {previousSessionProfit:F2} -> {currentSessionProfit:F2} (variação: +{profitVariation:F2}) -> REDUZIR stakes: {adjustmentFactor:F4}");
            }
            else if (profitVariation < 0) // Prejuízo AUMENTOU (SessionProfit ficou mais negativo)
            {
                // Exemplo: era -0.38, agora é -0.45 -> aumentar stakes (tentar recuperar)
                decimal worseningRatio = Math.Min(Math.Abs(profitVariation) / Math.Abs(previousSessionProfit), 0.3m);
                adjustmentFactor = Math.Min(1.15m, 1.0m + worseningRatio * 0.15m); // Aumentar até 15%
                _logger.LogInformation($"[ADJUSTMENT] Prejuízo AUMENTOU: {previousSessionProfit:F2} -> {currentSessionProfit:F2} (variação: {profitVariation:F2}) -> AUMENTAR stakes: {adjustmentFactor:F4}");
            }
            else
            {
                // Sem variação significativa
                adjustmentFactor = 1.0m;
                _logger.LogInformation($"[ADJUSTMENT] Prejuízo ESTÁVEL: {currentSessionProfit:F2} -> manter stakes: {adjustmentFactor:F4}");
            }
        }
        else if (currentSessionProfit > 0) // Estamos em lucro
        {
            // Em lucro, manter stakes estáveis com leve aumento baseado no lucro acumulado
            decimal profitRatio = Math.Min(currentSessionProfit / DualTakeProfit, 1.0m);
            adjustmentFactor = Math.Min(1.05m, 1.0m + profitRatio * 0.05m);
            _logger.LogInformation($"[ADJUSTMENT] Em LUCRO: {currentSessionProfit:F2} -> manter stakes estáveis: {adjustmentFactor:F4}");
        }
        
        // Garantir que o fator esteja dentro de limites seguros
        adjustmentFactor = Math.Max(0.85m, Math.Min(1.20m, adjustmentFactor));
        
        _logger.LogInformation($"[ADJUSTMENT] SessionProfit: {previousSessionProfit:F2} -> {currentSessionProfit:F2}, Variação: {profitVariation:F2}, Fator final: {adjustmentFactor:F4}");
        
        return adjustmentFactor;
    }

    /// <summary>
    /// Estratégia tradicional como fallback
    /// </summary>
    private (decimal stake1, decimal stake2) CalculateTraditionalStakes(
        decimal targetProfit, decimal baseStake, decimal payout1, decimal payout2)
    {
        // Calcular fator de ajuste baseado no SessionProfit
        decimal adjustmentFactor = CalculateSessionProfitAdjustmentFactor();
        
        decimal lowerStake = Math.Max(Math.Round(baseStake * adjustmentFactor, 2), 0.35m);
        
        // Todas as rodadas usam proporção de 80%
        decimal proportion = 0.80m;
        
        decimal higherStake = Math.Round(lowerStake / proportion, 2);
        
        _logger.LogInformation($"[TRADITIONAL STAKES] Fator de ajuste SessionProfit: {adjustmentFactor:F4}");
        
        _logger.LogInformation($"[TRADITIONAL CALC] Stakes calculadas - Lower: {lowerStake:F2}, Higher: {higherStake:F2} (proporção {(proportion*100):F0}%)");
        
        return (higherStake, lowerStake);
    }


    /// <summary>
    /// Arredonda para cima com 2 casas decimais
    /// </summary>
    private static decimal ArredondarParaCima2Casas(decimal valor)
    {
        return Math.Ceiling(valor * 100) / 100;
    }
    
    /// <summary>
    /// Atualiza a análise de volatilidade do mercado baseada no histórico de preços
    /// </summary>
    private void UpdateVolatilityAnalysis(decimal newPrice)
    {
        if (newPrice <= 0) return;
        
        // Adicionar novo preço ao histórico
        _priceHistory.Enqueue(newPrice);
        _priceTimestamps.Enqueue(DateTime.Now);
        
        // Manter apenas os últimos N preços
        while (_priceHistory.Count > _maxPriceHistorySize)
        {
            _priceHistory.Dequeue();
            _priceTimestamps.Dequeue();
        }
        
        // Calcular preço médio
        if (_priceHistory.Count > 0)
        {
            AveragePrice = _priceHistory.Average();
        }
        
        // Calcular índice de volatilidade (desvio padrão normalizado)
        if (_priceHistory.Count >= 10)
        {
            var prices = _priceHistory.ToArray();
            var mean = (double)AveragePrice;
            var variance = prices.Select(p => Math.Pow((double)p - mean, 2)).Average();
            var standardDeviation = Math.Sqrt(variance);
            
            // Normalizar pela média para obter volatilidade relativa
            VolatilityIndex = mean > 0 ? (decimal)(standardDeviation / mean) : 0;
            
            _logger.LogInformation($"[VOLATILIDADE] Preço: {newPrice:F5}, Média: {AveragePrice:F5}, Volatilidade: {VolatilityIndex:F6}");
        }
    }
    
    /// <summary>
    /// Calcula fator de ajuste baseado na volatilidade do mercado
    /// </summary>
    private decimal CalculateVolatilityAdjustmentFactor()
    {
        if (VolatilityIndex == 0 || _priceHistory.Count < 10)
        {
            return 1.0m; // Sem ajuste se não há dados suficientes
        }
        
        decimal adjustmentFactor = 1.0m;
        
        // Classificar volatilidade
        if (VolatilityIndex > 0.01m) // Alta volatilidade (>1%)
        {
            // Reduzir agressividade em mercados muito voláteis
            adjustmentFactor = Math.Max(0.7m, 1.0m - (VolatilityIndex * 20m));
            _logger.LogInformation($"[VOLATILIDADE] Alta volatilidade detectada: {VolatilityIndex:P2} -> Reduzir agressividade: {adjustmentFactor:F3}");
        }
        else if (VolatilityIndex < 0.002m) // Baixa volatilidade (<0.2%)
        {
            // Aumentar ligeiramente a agressividade em mercados estáveis
            adjustmentFactor = Math.Min(1.15m, 1.0m + (0.002m - VolatilityIndex) * 50m);
            _logger.LogInformation($"[VOLATILIDADE] Baixa volatilidade detectada: {VolatilityIndex:P2} -> Aumentar agressividade: {adjustmentFactor:F3}");
        }
        else
        {
            // Volatilidade normal - sem ajuste
            _logger.LogInformation($"[VOLATILIDADE] Volatilidade normal: {VolatilityIndex:P2} -> Sem ajuste");
        }
        
        return adjustmentFactor;
     }
     
     /// <summary>
     /// Atualiza o tracking de drawdown e ativa proteção se necessário
     /// </summary>
     private void UpdateDrawdownTracking()
     {
         decimal currentBalance = SessionProfit;
         
         // Atualizar pico de balance se necessário
         if (currentBalance > _peakBalance)
         {
             _peakBalance = currentBalance;
         }
         
         // Calcular drawdown atual
         CurrentDrawdown = _peakBalance - currentBalance;
         
         // Atualizar drawdown máximo histórico
         if (CurrentDrawdown > MaxHistoricalDrawdown)
         {
             MaxHistoricalDrawdown = CurrentDrawdown;
             _logger.LogWarning($"[DRAWDOWN] Novo drawdown máximo histórico: {MaxHistoricalDrawdown:F2}");
         }
         
         // Ativar proteção se drawdown atual exceder limites (muito mais tolerante)
        decimal drawdownLimit = Math.Max(MaxHistoricalDrawdown * 3.0m, Math.Abs(DualTakeProfit) * 8.0m); // Aumentado de 1.2x para 3.0x e de 2.0x para 8.0x
        DrawdownProtectionActive = CurrentDrawdown > drawdownLimit;
         
         if (DrawdownProtectionActive)
         {
             _logger.LogWarning($"[DRAWDOWN PROTECTION] ATIVADA! Drawdown atual: {CurrentDrawdown:F2}, Limite: {drawdownLimit:F2}");
         }
         
         _logger.LogInformation($"[DRAWDOWN] Atual: {CurrentDrawdown:F2}, Máximo histórico: {MaxHistoricalDrawdown:F2}, Proteção: {(DrawdownProtectionActive ? "ATIVA" : "Inativa")}");
     }
     
     /// <summary>
     /// Calcula fator de proteção baseado no drawdown atual
     /// </summary>
     private decimal CalculateDrawdownProtectionFactor()
     {
         if (!DrawdownProtectionActive || CurrentDrawdown == 0)
         {
             return 1.0m;
         }
         
         // Calcular intensidade da proteção baseada no drawdown
         decimal drawdownRatio = CurrentDrawdown / Math.Max(MaxHistoricalDrawdown, Math.Abs(DualTakeProfit));
         
         // Reduzir agressividade progressivamente
         decimal protectionFactor = Math.Max(0.5m, 1.0m - (drawdownRatio * 0.3m));
         
         _logger.LogInformation($"[DRAWDOWN PROTECTION] Ratio: {drawdownRatio:F3}, Fator de proteção: {protectionFactor:F3}");
         
         return protectionFactor;
      }
      
      /// <summary>
      /// Classe para armazenar dados de performance de uma sessão
      /// </summary>
      private class SessionPerformanceData
      {
          public DateTime SessionDate { get; set; }
          public decimal SessionProfit { get; set; }
          public int TotalTrades { get; set; }
          public int WinningTrades { get; set; }
          public decimal MaxDrawdown { get; set; }
          public decimal WinRate => TotalTrades > 0 ? (decimal)WinningTrades / TotalTrades : 0;
      }
      
      /// <summary>
      /// Registra o final de uma sessão e atualiza estatísticas multi-sessão
      /// </summary>
      private void RecordSessionEnd()
      {
          var sessionData = new SessionPerformanceData
          {
              SessionDate = DateTime.Now,
              SessionProfit = SessionProfit,
              TotalTrades = ProfitTableEntries?.Count ?? 0,
              WinningTrades = ProfitTableEntries?.Count(e => e.TotalProfitLoss > 0) ?? 0,
              MaxDrawdown = CurrentDrawdown
          };
          
          _sessionHistory.Enqueue(sessionData);
          
          // Manter apenas as últimas N sessões
          while (_sessionHistory.Count > _maxSessionHistory)
          {
              _sessionHistory.Dequeue();
          }
          
          // Atualizar estatísticas cumulativas
          UpdateMultiSessionStatistics();
          
          _logger.LogInformation($"[MULTI-SESSION] Sessão registrada - Profit: {SessionProfit:F2}, Trades: {sessionData.TotalTrades}, WinRate: {sessionData.WinRate:P1}");
      }
      
      /// <summary>
      /// Atualiza estatísticas baseadas no histórico de múltiplas sessões
      /// </summary>
      private void UpdateMultiSessionStatistics()
      {
          if (_sessionHistory.Count == 0) return;
          
          // Calcular profit cumulativo
          CumulativeSessionProfit = _sessionHistory.Sum(s => s.SessionProfit);
          
          // Contar sessões consecutivas com prejuízo
          ConsecutiveLossSessions = 0;
          foreach (var session in _sessionHistory.Reverse())
          {
              if (session.SessionProfit < 0)
              {
                  ConsecutiveLossSessions++;
              }
              else
              {
                  break;
              }
          }
          
          // Calcular fator de recuperação multi-sessão
          CalculateMultiSessionRecoveryFactor();
      }
      
      /// <summary>
      /// Calcula fator de recuperação baseado na performance de múltiplas sessões
      /// </summary>
      private void CalculateMultiSessionRecoveryFactor()
      {
          if (_sessionHistory.Count < 3)
          {
              MultiSessionRecoveryFactor = 1.0m;
              return;
          }
          
          decimal baseFactor = 1.0m;
          
          // Ajustar baseado em sessões consecutivas com prejuízo
          if (ConsecutiveLossSessions > 0)
          {
              // Aumentar gradualmente a agressividade após sessões ruins
              decimal lossSessionFactor = Math.Min(ConsecutiveLossSessions * 0.05m, 0.25m); // Máximo 25%
              baseFactor += lossSessionFactor;
          }
          
          // Ajustar baseado no profit cumulativo
          if (CumulativeSessionProfit < 0)
          {
              decimal cumulativeLossRatio = Math.Abs(CumulativeSessionProfit) / Math.Max(Math.Abs(DualTakeProfit) * 5, 1);
              decimal cumulativeFactor = Math.Min(cumulativeLossRatio * 0.1m, 0.15m); // Máximo 15%
              baseFactor += cumulativeFactor;
          }
          else if (CumulativeSessionProfit > 0)
          {
              // Reduzir ligeiramente a agressividade quando há lucro cumulativo
              decimal profitRatio = CumulativeSessionProfit / Math.Max(Math.Abs(DualTakeProfit) * 3, 1);
              decimal profitReduction = Math.Min(profitRatio * 0.05m, 0.1m); // Máximo 10% de redução
              baseFactor -= profitReduction;
          }
          
          // Calcular win rate das últimas sessões
          var recentSessions = _sessionHistory.TakeLast(5).ToList();
          decimal avgWinRate = recentSessions.Count > 0 ? recentSessions.Average(s => s.WinRate) : 0.5m;
          
          // Ajustar baseado na win rate recente
          if (avgWinRate < 0.4m) // Win rate baixa
          {
              baseFactor *= 0.9m; // Reduzir agressividade
          }
          else if (avgWinRate > 0.6m) // Win rate alta
          {
              baseFactor *= 1.05m; // Aumentar ligeiramente
          }
          
          // Limitar o fator dentro de bounds seguros
          MultiSessionRecoveryFactor = Math.Max(0.8m, Math.Min(1.3m, baseFactor));
          
          _logger.LogInformation($"[MULTI-SESSION RECOVERY] Sessões perdidas consecutivas: {ConsecutiveLossSessions}, Profit cumulativo: {CumulativeSessionProfit:F2}, WinRate média: {avgWinRate:P1}, Fator: {MultiSessionRecoveryFactor:F3}");
       }
       
       /// <summary>
       /// Verifica se deve ativar pausa automática baseada nas condições de mercado
       /// </summary>
       // Método removido - pausa automática desabilitada
       
       /// <summary>
       /// Método removido - cálculo de score de condições de mercado desabilitado
       /// </summary>
       private void CalculateMarketConditionScore()
       {
           // Método removido - pausa automática desabilitada
       }
       
       // Método removido - pausa automática desabilitada
       
       // Método removido - pausa automática desabilitada
       
       // Método removido - pausa automática desabilitada
       
       // Método removido - pausa automática desabilitada
       
       /// <summary>
       /// Verifica se o trading deve ser reativado automaticamente
       /// </summary>
       private void CheckTradingReactivation()
       {
           try
           {
               // Só reativar se estiver conectado
               if (IsConnected && !IsTradingEnabled)
               {
                   // Reativar se está em modo dual ativo
                   if (IsDualEnabled && CurrentDualLevel > 0)
                   {
                       _logger.LogInformation("[AUTO-REACTIVATE] Reativando trading - modo dual ativo (Level: {CurrentDualLevel})", CurrentDualLevel);
                       IsTradingEnabled = true;
                       return;
                   }
                   
                   // Reativação automática removida - pausa automática desabilitada
                   // if (MarketConditionScore > 0.5m && _consecutiveLossesForPause < _maxConsecutiveLossesBeforePause / 2)
                   // {
                   //     _logger.LogInformation("[AUTO-REACTIVATE] Reativando trading - condições de mercado favoráveis (Score: {MarketConditionScore:F3})", MarketConditionScore);
                   //     IsTradingEnabled = true;
                   //     return;
                   // }
               }
           }
           catch (Exception ex)
           {
               _logger.LogError(ex, "[AUTO-REACTIVATE] Erro ao verificar reativação do trading");
           }
       }
       
       /// <summary>
       /// Método removido - contador de perdas consecutivas desabilitado
       /// </summary>
       private void UpdateConsecutiveLossesForPause(bool isWin)
       {
           // Método removido - pausa automática desabilitada
       }
       
       /// <summary>
       /// Atualiza o sistema de recuperação escalonada baseado no resultado da operação
       /// </summary>
       private void UpdateRecoverySystem(bool isWin, decimal profit)
       {
           // Atualizar taxa de vitórias recentes
           _recentWinRates.Add(isWin ? 1m : 0m);
           if (_recentWinRates.Count > _winRateCalculationPeriod)
           {
               _recentWinRates.RemoveAt(0);
           }
           
           var currentWinRate = CurrentWinRate;
           
           if (!_isRecoveryMode)
           {
               // Verificar se deve entrar em modo recuperação
               if (currentWinRate < 0.4m && _recentWinRates.Count >= 10) // 40% de taxa de vitória
               {
                   ActivateRecoveryMode();
               }
           }
           else
           {
               // Já está em modo recuperação
               if (isWin)
               {
                   // Vitória - verificar se pode reduzir nível ou sair do modo
                   if (currentWinRate >= _recoveryThreshold)
                   {
                       DeactivateRecoveryMode();
                   }
                   else if (_recoveryLevel > 0 && currentWinRate > 0.5m)
                   {
                       _recoveryLevel = Math.Max(0, _recoveryLevel - 1);
                       RecoveryLevel = _recoveryLevel;
                   }
               }
               else
               {
                   // Perda - aumentar nível de recuperação se necessário
                   if (_recoveryLevel < _maxRecoveryLevel && currentWinRate < 0.3m)
                   {
                       _recoveryLevel++;
                       RecoveryLevel = _recoveryLevel;
                   }
               }
           }
           
           LogRecoveryStatus();
            
            // Atualizar fator de confiança
            UpdateConfidenceFactor(isWin);
        }
       
       /// <summary>
       /// Ativa o modo de recuperação escalonada
       /// </summary>
       private void ActivateRecoveryMode()
       {
           _isRecoveryMode = true;
           _recoveryLevel = 1;
           _recoveryModeStartTime = DateTime.Now;
           _recoveryModeStartBalance = (decimal)Balance;
           
           IsRecoveryMode = true;
           RecoveryLevel = _recoveryLevel;
           
           _logger.LogWarning("[RECOVERY] Modo de recuperação escalonada ATIVADO - Taxa de vitória: {WinRate:P2}", CurrentWinRate);
       }
       
       /// <summary>
       /// Desativa o modo de recuperação escalonada
       /// </summary>
       private void DeactivateRecoveryMode()
       {
           var recoveryDuration = DateTime.Now - _recoveryModeStartTime;
           var balanceRecovered = (decimal)Balance - _recoveryModeStartBalance;
           
           _isRecoveryMode = false;
           _recoveryLevel = 0;
           
           IsRecoveryMode = false;
           RecoveryLevel = _recoveryLevel;
           
           _logger.LogInformation("[RECOVERY] Modo de recuperação DESATIVADO - Duração: {Duration}, Saldo recuperado: {Recovery:C2}, Taxa final: {WinRate:P2}", 
               recoveryDuration, balanceRecovered, CurrentWinRate);
       }
       
       /// <summary>
        /// Registra o status atual do sistema de recuperação
        /// </summary>
        private void LogRecoveryStatus()
        {
            if (_isRecoveryMode)
            {
                _logger.LogInformation("[RECOVERY] Nível: {Level}/{MaxLevel}, Multiplicador: {Multiplier:F2}x, Taxa de vitória: {WinRate:P2}", 
                    _recoveryLevel, _maxRecoveryLevel, CurrentRecoveryMultiplier, CurrentWinRate);
            }
        }
        
        /// <summary>
        /// Atualiza o fator de confiança baseado na consistência das vitórias recentes
        /// </summary>
        private void UpdateConfidenceFactor(bool isWin)
        {
            // Adicionar resultado recente
            _recentResults.Add(isWin);
            if (_recentResults.Count > _confidenceCalculationPeriod)
            {
                _recentResults.RemoveAt(0);
            }
            
            if (_recentResults.Count < 5) // Dados insuficientes
            {
                ConfidenceFactor = 1.0m;
                return;
            }
            
            // Calcular consistência baseada em sequências
            var consistency = CalculateConsistencyScore();
            var winRate = _recentResults.Count(r => r) / (decimal)_recentResults.Count;
            
            // Fator base baseado na taxa de vitória
            decimal baseFactor = 0.5m + (winRate * 0.8m); // 0.5 a 1.3
            
            // Ajuste pela consistência (penaliza alternância excessiva)
            decimal consistencyMultiplier = 0.7m + (consistency * 0.6m); // 0.7 a 1.3
            
            // Calcular fator final
            decimal newFactor = baseFactor * consistencyMultiplier;
            newFactor = Math.Max(_minConfidenceFactor, Math.Min(_maxConfidenceFactor, newFactor));
            
            ConfidenceFactor = Math.Round(newFactor, 3);
            
            _logger.LogInformation("[CONFIDENCE] Taxa: {WinRate:P1}, Consistência: {Consistency:F3}, Fator: {Factor:F3}", 
                winRate, consistency, ConfidenceFactor);
        }
        
        /// <summary>
        /// Calcula score de consistência baseado em sequências de vitórias/derrotas
        /// </summary>
        private decimal CalculateConsistencyScore()
        {
            if (_recentResults.Count < 3) return 0.5m;
            
            // Contar sequências
            var sequences = new List<int>();
            int currentSequence = 1;
            
            for (int i = 1; i < _recentResults.Count; i++)
            {
                if (_recentResults[i] == _recentResults[i - 1])
                {
                    currentSequence++;
                }
                else
                {
                    sequences.Add(currentSequence);
                    currentSequence = 1;
                }
            }
            sequences.Add(currentSequence);
            
            // Sequências mais longas indicam maior consistência
            var avgSequenceLength = sequences.Average();
            var maxSequenceLength = sequences.Max();
            
            // Score baseado no comprimento médio e máximo das sequências
            decimal consistencyScore = ((decimal)avgSequenceLength / _recentResults.Count) * 0.7m + 
                                     ((decimal)maxSequenceLength / (decimal)_recentResults.Count) * 0.3m;
            
            return Math.Max(0m, Math.Min(1m, consistencyScore));
         }
         
         /// <summary>
         /// Atualiza o score de momentum baseado na análise de preços recentes
         /// </summary>
         private void UpdateMomentumScore()
         {
             if (DateTime.Now - _lastTimingCheck < _timingCheckInterval)
                 return;
                 
             _lastTimingCheck = DateTime.Now;
             
             // Calcular momentum baseado na volatilidade e direção do preço
             var currentMomentum = CalculateCurrentMomentum();
             
             // Adicionar ao histórico
             _momentumHistory.Add(currentMomentum);
             if (_momentumHistory.Count > _momentumCalculationPeriod)
             {
                 _momentumHistory.RemoveAt(0);
             }
             
             // Calcular score médio com peso maior para valores recentes
             if (_momentumHistory.Count > 0)
             {
                 decimal weightedSum = 0m;
                 decimal totalWeight = 0m;
                 
                 for (int i = 0; i < _momentumHistory.Count; i++)
                 {
                     decimal weight = (i + 1) / (decimal)_momentumHistory.Count; // Peso crescente
                     weightedSum += _momentumHistory[i] * weight;
                     totalWeight += weight;
                 }
                 
                 MomentumScore = Math.Round(weightedSum / totalWeight, 3);
             }
             
             _logger.LogDebug("[MOMENTUM] Score atual: {Score:F3}, Timing ótimo: {IsOptimal}", 
                 MomentumScore, IsOptimalTiming);
         }
         
         /// <summary>
         /// Calcula o momentum atual baseado em volatilidade e tendência
         /// </summary>
         private decimal CalculateCurrentMomentum()
         {
             // Fatores que influenciam o momentum:
             // 1. Volatilidade moderada (nem muito alta, nem muito baixa)
             // 2. Tendência clara
             // 3. Volume de negociação (se disponível)
             // 4. Condições de mercado
             
             decimal volatilityScore = CalculateVolatilityScore();
             decimal trendScore = CalculateTrendScore();
             decimal marketScore = 1.0m; // MarketConditionScore removido
             
             // Combinar scores com pesos
             decimal momentum = (volatilityScore * 0.4m) + (trendScore * 0.3m) + (marketScore * 0.3m);
             
             return Math.Max(0m, Math.Min(1m, momentum));
         }
         
         /// <summary>
         /// Calcula score de volatilidade (moderada é melhor)
         /// </summary>
         private decimal CalculateVolatilityScore()
         {
             // Volatilidade ideal está entre 0.3 e 0.7
             decimal currentVolatility = Math.Abs(_priceDirection);
             
             if (currentVolatility < 0.1m) return 0.2m; // Muito baixa
             if (currentVolatility > 0.9m) return 0.3m; // Muito alta
             
             // Curva que favorece volatilidade moderada
             if (currentVolatility >= 0.3m && currentVolatility <= 0.7m)
                 return 0.8m + (0.2m * (1 - Math.Abs(0.5m - currentVolatility) * 5));
             
             return 0.5m;
         }
         
         /// <summary>
         /// Calcula score de tendência baseado na direção consistente
         /// </summary>
         private decimal CalculateTrendScore()
         {
             // Usar a direção do preço e consistência
             decimal directionStrength = Math.Abs(_priceDirection);
             
             // Tendência mais forte = melhor timing
             if (directionStrength > 0.7m) return 0.9m;
             if (directionStrength > 0.5m) return 0.7m;
             if (directionStrength > 0.3m) return 0.5m;
             
             return 0.3m;
         }
         
         /// <summary>
         /// Verifica se é um bom momento para entrar em uma operação
         /// </summary>
         private bool ShouldWaitForBetterTiming()
         {
             UpdateMomentumScore();
             
             // Se o momentum está bom, pode operar
             if (IsOptimalTiming)
             {
                 IsWaitingForOptimalTiming = false;
                 return false;
             }
             
             // Se está em modo de recuperação, ser menos rigoroso com timing
            if (_isRecoveryMode && MomentumScore > 0.25m)
            {
                IsWaitingForOptimalTiming = false;
                return false;
            }
            
            // Para modo dual, ser muito mais permissivo com timing (permitir quase sempre)
            if (IsDualEnabled && MomentumScore > 0.1m)
            {
                IsWaitingForOptimalTiming = false;
                _logger.LogInformation("[TIMING] Permitindo entrada no modo dual com score: {Score:F3}", MomentumScore);
                return false;
            }
            
            // Aguardar timing melhor apenas se score muito baixo
            if (MomentumScore < 0.15m)
            {
                IsWaitingForOptimalTiming = true;
                _logger.LogInformation("[TIMING] Aguardando momento mais favorável. Score atual: {Score:F3}", MomentumScore);
                return true;
            }
            
            // Permitir entrada com score moderado
            IsWaitingForOptimalTiming = false;
            return false;
         }
       
       /// <summary>
       /// Calcula score de momentum baseado na direção dos preços recentes
       /// </summary>
       private decimal CalculateMomentumScore()
       {
           if (_priceHistory.Count < 10)
               return 1.0m; // Dados insuficientes, assumir neutro
               
           var prices = _priceHistory.ToArray();
           var recentPrices = prices.TakeLast(10).ToArray();
           
           // Calcular tendência usando regressão linear simples
           decimal trend = 0;
           for (int i = 1; i < recentPrices.Length; i++)
           {
               trend += recentPrices[i] - recentPrices[i-1];
           }
           
           // Calcular volatilidade do momentum
           decimal avgChange = trend / (recentPrices.Length - 1);
           decimal volatility = 0;
           for (int i = 1; i < recentPrices.Length; i++)
           {
               decimal change = recentPrices[i] - recentPrices[i-1];
               volatility += Math.Abs(change - avgChange);
           }
           volatility /= (recentPrices.Length - 1);
           
           // Score baseado na estabilidade do momentum
           decimal momentumScore = 1.0m;
           if (volatility > AveragePrice * 0.001m) // Alta volatilidade
               momentumScore *= 0.8m;
           else if (volatility < AveragePrice * 0.0005m) // Baixa volatilidade
               momentumScore *= 1.1m;
               
           return Math.Max(0.5m, Math.Min(1.3m, momentumScore));
       }
       
       /// <summary>
       /// Calcula score de consistência baseado no padrão das vitórias recentes
       /// </summary>
       private decimal CalculateWinConsistencyScore()
       {
           var recentEntries = ProfitTableEntries
               .Where(e => e.ExitSpot.HasValue)
               .OrderByDescending(e => e.ExitSpot)
               .Take(20)
               .ToList();
               
           if (recentEntries.Count < 10)
               return 1.0m; // Dados insuficientes
               
           // Analisar padrão de vitórias/derrotas
           var results = recentEntries.Select(e => e.TotalProfitLoss > 0).ToArray();
           
           // Calcular streaks (sequências)
           var streaks = new List<int>();
           int currentStreak = 1;
           bool currentType = results[0];
           
           for (int i = 1; i < results.Length; i++)
           {
               if (results[i] == currentType)
               {
                   currentStreak++;
               }
               else
               {
                   streaks.Add(currentStreak);
                   currentStreak = 1;
                   currentType = results[i];
               }
           }
           streaks.Add(currentStreak);
           
           // Score baseado na variabilidade das streaks
           decimal avgStreak = (decimal)streaks.Average();
           decimal maxStreak = streaks.Max();
           
           decimal consistencyScore = 1.0m;
           if (maxStreak > 6) // Streaks muito longas indicam inconsistência
               consistencyScore *= 0.7m;
           else if (avgStreak < 2.5m) // Muita alternância é boa
               consistencyScore *= 1.2m;
               
           return Math.Max(0.6m, Math.Min(1.4m, consistencyScore));
       }
       
       /// <summary>
       /// Calcula score baseado no horário de mercado (alguns horários são melhores)
       /// </summary>
       private decimal CalculateMarketTimingScore()
       {
           var now = DateTime.Now;
           var hour = now.Hour;
           
           // Horários baseados em GMT (ajustar conforme necessário)
           decimal timingScore = 1.0m;
           
           // Horários de alta liquidez (sobreposição de mercados)
           if ((hour >= 8 && hour <= 12) || (hour >= 13 && hour <= 17))
           {
               timingScore *= 1.1m; // Melhores horários
           }
           // Horários de baixa liquidez
           else if (hour >= 22 || hour <= 6)
           {
               timingScore *= 0.9m; // Horários menos favoráveis
           }
           
           // Evitar horários de notícias importantes (exemplo: 15:30 GMT)
           if (hour == 15 && now.Minute >= 25 && now.Minute <= 35)
           {
               timingScore *= 0.8m; // Horário de notícias econômicas
           }
           
           return Math.Max(0.7m, Math.Min(1.2m, timingScore));
       }
   
       private async Task<ProposalResponse?> GetProposalForContract(ContractDetails contractType, decimal stake)
    {
        try
        {
            _logger.LogInformation($"[DUAL] Obtendo proposta para {contractType.ContractDisplay} com stake {stake:F2}");
            
            var request = new ProposalRequest
            {
                Stake = stake,
                Symbol = SelectedActiveSymbol!.Symbol,
                ContractType = contractType.ContractType,
                Currency = "USD",
                Duration = DurationValue,
                DurationUnit = DurationUnit
            };

            // Adicionar parâmetros específicos do contrato
            if (IsBarrier1Visible && !string.IsNullOrEmpty(Barrier1Value))
            {
                request.Barrier = Barrier1Value;
            }

            if (IsBarrier2Visible && !string.IsNullOrEmpty(Barrier2Value))
            {
                request.Barrier2 = Barrier2Value;
            }

            if (IsDigitSelectionVisible)
            {
                request.LastDigitPrediction = SelectedDigit;
            }

            var result = await _derivApiService.GetProposalAsync(request);
            
            if (result?.Error != null)
            {
                _logger.LogError($"[DUAL] Erro na proposta para {contractType.ContractDisplay}: {result.Error.Message}");
                return null;
            }
            
            if (result?.Proposal != null)
            {
                _logger.LogInformation($"[DUAL] Proposta obtida - {contractType.ContractDisplay}: ID={result.Proposal.Id}, AskPrice={result.Proposal.AskPrice:F2}, Payout={result.Proposal.Payout:F2}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DUAL] Erro ao obter proposta para contrato {contractType.ContractDisplay}");
            return null;
        }
    }

    // Métodos do Martingale
    private void CalculateNextStake()
    {
        if (!IsMartingaleEnabled)
        {
            NextStakeAmount = TryGetStakeAmountDecimal(out decimal currentStake) ? currentStake : 0;
            return;
        }
        if (InitialStakeAmount == 0)
        {
            InitialStakeAmount = Stake > 0 ? Stake : (TryGetStakeAmountDecimal(out decimal initialStake) ? initialStake : MinStakeAllowed);
        }

        if (CurrentMartingaleLevel == 0)
        {
            NextStakeAmount = InitialStakeAmount;
        }
        else
        {
            NextStakeAmount = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, CurrentMartingaleLevel);
        }
    }

    // Garante a proporção correta entre stakes
    // Regra: stake menor = 80% da stake maior, mas nunca acima de pequenos arredondamentos do Stake de referência.
    // Para evitar que a stake mínima (campo Stake) suba de 0.35 para 0.36 por arredondamento,
    // arredondamos a stake menor para baixo (floor em 2 casas) e a fixamos exatamente em Stake quando próximo.
    private void EnforceStakeProportion(ref decimal stake1, ref decimal stake2)
    {
        decimal proportion = 0.80m;

        bool stake1IsHigher = stake1 >= stake2;
        decimal higher = stake1IsHigher ? stake1 : stake2;

        // Stake menor mínima deve respeitar o campo Stake do usuário
        decimal minLower = Math.Max(MinStakeAllowed, Stake);

        // 1) Calcular lower bruto pela proporção e arredondar PARA BAIXO
        decimal rawLower = higher * proportion;
        decimal lower = Math.Floor(rawLower * 100m) / 100m; // evita subir 0.35 -> 0.36

        // 2) Fixar lower no mínimo quando ficar abaixo
        if (lower < minLower)
            lower = minLower;

        // 3) Garantir que a maior seja suficiente para manter a proporção com o lower escolhido
        decimal minHigherForLower = Math.Ceiling((lower / proportion) * 100m) / 100m;
        if (higher < minHigherForLower)
            higher = minHigherForLower;

        // Arredondamentos finais
        higher = Math.Round(higher, 2);
        lower = Math.Round(lower, 2);

        if (stake1IsHigher)
        {
            stake1 = higher;
            stake2 = lower;
        }
        else
        {
            stake2 = higher;
            stake1 = lower;
        }
    }

    // Helper seguro para tentar obter decimal a partir de StakeAmount
    private bool TryGetStakeAmountDecimal(out decimal result)
    {
        result = 0m;
        if (string.IsNullOrWhiteSpace(StakeAmount)) return false;
        var normalized = StakeAmount.Replace(',', '.');
        if (decimal.TryParse(normalized, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal parsed))
        {
            result = parsed;
            return true;
        }
        return false;
    }

    // IMMEDIATE HOT PROPOSAL POOL - Synchronous population for instant execution
    private async Task PopulateHotProposalPoolImmediate()
    {
        if (_isPoolPopulating || !IsFastMartingale || !IsMartingaleEnabled) return;
        
        _isPoolPopulating = true;
        var poolStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at {poolStartTime:HH:mm:ss.fff}");
        
        try
        {
            // Validate required fields
            if (SelectedContractType?.ContractType == null || SelectedActiveSymbol?.Symbol == null)
            {
                _logger.LogWarning("[DEBUG] HOT POOL IMMEDIATE: Missing required fields - canceling population");
                return;
            }
            
            // AGGRESSIVE POPULATION: Clear and rebuild entire pool for freshness
            lock (_poolLock)
            {
                _hotProposalPool.Clear();
                _logger.LogInformation("[DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild");
            }
            
            // PRE-EMPTIVE POPULATION: Calculate for valid next levels only
            var populationTasks = new List<Task>();
            
            // Determine maximum valid level based on stake constraints
            int maxValidLevel = 5;
            for (int testLevel = 1; testLevel <= 5; testLevel++)
            {
                var testStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, testLevel);
                if (testStake < MinStakeAllowed)
                {
                    maxValidLevel = testLevel - 1;
                    _logger.LogWarning($"[DEBUG] HOT POOL: Level {testLevel} would result in stake {testStake:F2} below minimum {MinStakeAllowed:F2}. Limiting to level {maxValidLevel}");
                    break;
                }
            }
            
            for (int level = 1; level <= maxValidLevel; level++)
            {
                var levelTask = Task.Run(async () =>
                {
                    try
                    {
                        var levelStartTime = DateTimeOffset.Now;
                        var futureStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level);
                        
                        // Additional validation to ensure stake meets minimum requirements
                        if (futureStake < MinStakeAllowed)
                        {
                            _logger.LogWarning($"[DEBUG] HOT POOL: Skipping level {level} - calculated stake {futureStake:F2} below minimum {MinStakeAllowed:F2}");
                            return;
                        }
                        
                        var request = new ProposalRequest
                        {
                            ContractType = SelectedContractType.ContractType,
                            Symbol = SelectedActiveSymbol.Symbol,
                            Duration = DurationValue,
                            DurationUnit = DurationUnit,
                            Currency = "USD",
                            Stake = futureStake,
                            Barrier = IsBarrier1Visible ? Barrier1Value : null,
                            Barrier2 = IsBarrier2Visible ? Barrier2Value : null
                        };

                        if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
                        {
                            request.LastDigitPrediction = SelectedDigit;
                        }
                        
                        // FAST PROPOSAL REQUEST: Use high-speed API
                        var response = await _derivApiService.GetFastProposalAsync(request);
                        
                        if (response?.Error == null && response?.Proposal != null)
                        {
                            lock (_poolLock)
                            {
                                _hotProposalPool[level] = response;
                            }
                            
                            var levelEndTime = DateTimeOffset.Now;
                            var levelDelay = (levelEndTime - levelStartTime).TotalMilliseconds;
                            _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Level {level} populated in {levelDelay}ms. Stake: {futureStake:F2}, ProposalId: {response.Proposal.Id}");
                        }
                        else
                        {
                            _logger.LogError($"[DEBUG] HOT POOL AGGRESSIVE: Error populating level {level}: {response?.Error?.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[DEBUG] HOT POOL AGGRESSIVE: Exception in level {level}");
                    }
                });
                
                populationTasks.Add(levelTask);
            }
            
            // PARALLEL EXECUTION: Wait for all levels to complete
            await Task.WhenAll(populationTasks);
            
            var poolEndTime = DateTimeOffset.Now;
            var totalPoolTime = (poolEndTime - poolStartTime).TotalMilliseconds;
            
            lock (_poolLock)
            {
                _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Population completed in {totalPoolTime}ms. Pool contains {_hotProposalPool.Count} proposals GUARANTEED ready. Levels: [{string.Join(", ", _hotProposalPool.Keys)}]");
                
                // VALIDATION: Ensure we have at least the next 2 levels ready
                var nextLevel = CurrentMartingaleLevel + 1;
                var hasNextLevel = _hotProposalPool.ContainsKey(nextLevel);
                var hasSecondLevel = _hotProposalPool.ContainsKey(nextLevel + 1);
                
                if (!hasNextLevel || !hasSecondLevel)
                {
                    _logger.LogWarning($"[DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: {hasNextLevel}, Second: {hasSecondLevel}. Current: {CurrentMartingaleLevel}");
                }
                else
                {
                    _logger.LogInformation($"[DEBUG] HOT POOL VALIDATION: Critical levels ready. Pool is OPTIMAL for instant execution.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DEBUG] HOT POOL IMMEDIATE: Critical error in aggressive population");
        }
        finally
        {
            _isPoolPopulating = false;
        }
    }

    // LEGACY: Async hot proposal pool for backward compatibility
    private async Task PopulateHotProposalPool()
    {
        // Redirect to immediate synchronous version for consistency
        await PopulateHotProposalPoolImmediate();
    }
    
    // Replenish used proposal in the pool (optimized for background execution)
    private async Task ReplenishHotProposal(int level)
    {
        try
        {
            if (!IsFastMartingale || !IsMartingaleEnabled) return;
            
            var replenishStartTime = DateTimeOffset.Now;
            var futureStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level);
            
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType!.ContractType,
                Symbol = SelectedActiveSymbol!.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = futureStake,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            
            var response = await _derivApiService.GetFastProposalAsync(request);
            
            if (response?.Error == null && response?.Proposal != null)
            {
                lock (_poolLock)
                {
                    _hotProposalPool[level] = response;
                }
                
                var replenishEndTime = DateTimeOffset.Now;
                var replenishDelay = (replenishEndTime - replenishStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] HOT POOL REPLENISH: Nível {level} reabastecido em {replenishDelay}ms. ProposalId: {response.Proposal.Id}");
            }
            else
            {
                _logger.LogError($"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}: {response?.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}");
        }
    }

    // ULTRA-FAST LOSS EXECUTION: Zero-overhead processing for true sub-100ms execution
    private void OnContractLossUltraFast()
    {
        var startTime = DateTimeOffset.Now;
        
        // CRITICAL: Check if trading is enabled first (SOLID: Dependency Inversion)
        if (!IsTradingEnabled)
        {
            _logger.LogInformation("[TRADING STOPPED] Contract loss ignored - trading disabled by STOP command");
            return;
        }

        // Handle Dual mode
        if (IsDualEnabled)
        {
            OnDualContractResult(isWin: false);
            return;
        }
        
        // VALIDATION: Ultra-fast checks with minimal overhead
        if (!IsMartingaleEnabled || CurrentMartingaleLevel >= MartingaleLevel) 
        {
            if (CurrentMartingaleLevel >= MartingaleLevel)
            {
                ResetMartingale();
            }
            return;
        }

        // INSTANT STATE UPDATE: No property change notifications during critical path
        var previousLevel = CurrentMartingaleLevel;
        CurrentMartingaleLevel++;
        var newStake = NextStakeAmount.ToString("F2");
        _stakeAmount = newStake; // Direct field access to avoid property overhead
        
        if (!IsFastMartingale)
        {
            // Update UI for manual mode after state change
            Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                OnPropertyChanged(nameof(StakeAmount));
            }), System.Windows.Threading.DispatcherPriority.Background);
            return;
        }

        var stateUpdateTime = DateTimeOffset.Now;
        var stateDelay = (stateUpdateTime - startTime).TotalMilliseconds;
        _logger.LogInformation($"[TIMING] ULTRA-FAST: State updated in {stateDelay}ms. Level: {previousLevel} → {CurrentMartingaleLevel}, Stake: {newStake}");

        // ZERO-OVERHEAD POOL ACCESS: Direct dictionary access without try-catch overhead
        ProposalResponse? hotProposal = null;
        bool proposalFound = false;
        
        // Ultra-fast lock-free access when possible
        if (_hotProposalPool.ContainsKey(CurrentMartingaleLevel))
        {
            lock (_poolLock)
            {
                if (_hotProposalPool.TryGetValue(CurrentMartingaleLevel, out hotProposal))
                {
                    _hotProposalPool.Remove(CurrentMartingaleLevel);
                    proposalFound = true;
                }
            }
        }
        
        var retrievalTime = DateTimeOffset.Now;
        var retrievalDelay = (retrievalTime - stateUpdateTime).TotalMilliseconds;
        
        if (proposalFound && hotProposal?.Error == null && hotProposal?.Proposal != null)
        {
            _logger.LogInformation($"[TIMING] ULTRA-FAST: Hot proposal retrieved in {retrievalDelay}ms. ProposalId: {hotProposal.Proposal.Id}");
            
            // DIRECT WEBSOCKET EXECUTION: Bypass all intermediate layers
            var proposalId = hotProposal!.Proposal!.Id;
            var askPrice = hotProposal!.Proposal!.AskPrice;
            
            // IMMEDIATE WEBSOCKET SEND: Direct JSON construction and send
            var buyJson = $"{{\"buy\":\"{proposalId}\",\"price\":{askPrice},\"subscribe\":1}}";
            
            try
            {
                // Direct WebSocket access through service
                _derivApiService.SendDirectBuyCommand(buyJson);
                
                var executionTime = DateTimeOffset.Now;
                var totalTime = (executionTime - startTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ULTRA-FAST: Buy command sent in {totalTime}ms total - TRUE SUB-100MS EXECUTION");
                
                // MINIMAL UI UPDATE: Deferred to background with minimal data
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    CurrentProposalId = proposalId;
                    AskPrice = askPrice;
                    CalculatedPayout = hotProposal!.Proposal!.Payout;
                    OnPropertyChanged(nameof(StakeAmount)); // Update UI after execution
                }), System.Windows.Threading.DispatcherPriority.Background);
                
                // BACKGROUND REPLENISHMENT: Fire-and-forget
                _ = Task.Run(() => ReplenishHotProposal(CurrentMartingaleLevel));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CRITICAL] ULTRA-FAST: Direct WebSocket send failed - falling back to standard method");
                if (proposalId != null)
                {
                    _derivApiService.BuyContractImmediateAsync(proposalId, askPrice, _ => { });
                }
            }
        }
        else
        {
            var emergencyTime = DateTimeOffset.Now;
            var emergencyDelay = (emergencyTime - startTime).TotalMilliseconds;
            _logger.LogError($"[TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in {emergencyDelay}ms. Level: {CurrentMartingaleLevel}");
            
            // EMERGENCY FALLBACK: Direct execution with minimal overhead
            var emergencyRequest = new ProposalRequest
            {
                ContractType = SelectedContractType!.ContractType,
                Symbol = SelectedActiveSymbol!.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                // Use NextStakeAmount (decimal) which was computed earlier to avoid culture parsing
                Stake = Math.Round(Math.Max(NextStakeAmount > 0 ? NextStakeAmount : (TryGetStakeAmountDecimal(out var _tmp) ? _tmp : 0m), MinStakeAllowed), 2),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                emergencyRequest.LastDigitPrediction = SelectedDigit;
            }
            
            _derivApiService.BuyInstantMarketAsync(emergencyRequest, _ => { });
            
            var emergencyExecutionTime = DateTimeOffset.Now;
            var emergencyTotalTime = (emergencyExecutionTime - startTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] ULTRA-FAST EMERGENCY: Fallback sent in {emergencyTotalTime}ms");
            
            // Update UI after emergency execution
            Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                OnPropertyChanged(nameof(StakeAmount));
            }), System.Windows.Threading.DispatcherPriority.Background);
            
            // EMERGENCY POOL REBUILD: Background
            _ = Task.Run(() => PopulateHotProposalPoolImmediate());
        }
    }

    // IMMEDIATE LOSS EXECUTION: Zero-delay processing for fast martingale
    private void OnContractLossImmediate()
    {
        // Redirect to ultra-fast implementation for maximum performance
        OnContractLossUltraFast();
    }

    // LEGACY: Keep for backward compatibility
    public void OnContractLoss()
    {
        OnContractLossImmediate();
    }

    public void OnContractWin()
    {
        if (IsMartingaleEnabled)
        {
            // Reset to initial stake after a win
            ResetMartingale();
        }
        else if (IsDualEnabled)
        {
            // Handle dual mode win
            OnDualContractResult(isWin: true);
        }
    }

    private void ResetMartingale()
    {
        CurrentMartingaleLevel = 0;
        MaxLevel = 0; // Reset MaxLevel when martingale is reset
        StakeAmount = InitialStakeAmount.ToString("F2");
        CalculateNextStake();
    }

    // Métodos do Modo Dual
    private void OnDualContractResult(bool isWin)
    {
        // CORREÇÃO: Verificação mais robusta para contratos pendentes
        // Considerar tanto contratos pendentes quanto estado da sessão ativa
        bool hasActiveSession = CurrentDualSession > 0 && IsDualEnabled;
        bool shouldIgnore = !_isDualEntryPending && _pendingDualContracts.Count == 0 && !hasActiveSession;
        
        if (shouldIgnore)
        {
            _logger.LogInformation("[DUAL] No dual contracts pending and no active session, ignoring contract result");
            return;
        }
        
        // PROTEÇÃO: Se tem sessão ativa mas não há contratos pendentes, pode ser recuperação após timeout
        if (_pendingDualContracts.Count == 0 && hasActiveSession)
        {
            _logger.LogWarning("[DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery");
            Console.WriteLine($"[DUAL RECOVERY] Session {CurrentDualSession}, Level {CurrentDualLevel} - processing orphan contract result");
        }

        _logger.LogInformation($"[DUAL] Contract result received: {(isWin ? "WIN" : "LOSS")}, Pending contracts: {_pendingDualContracts.Count}");

        // A atualização do SessionProfit agora é feita em OnContractFinished
        // quando ambos os contratos finalizarem com delay apropriado
        
        // Log apenas para controle
        _logger.LogInformation($"[DUAL] Contracts completed: {_completedDualContracts}/{_pendingDualContracts.Count}");
    }

    private void CalculateDualSessionProfit()
    {
        _logger.LogInformation($"[DUAL] ============ CalculateDualSessionProfit called ============");
        _logger.LogInformation($"[DUAL] SessionProfit before calculation: {SessionProfit:F2}");
        _logger.LogInformation($"[DUAL] Calculating SessionProfit for Session {CurrentDualSession}");
        
        // CORREÇÃO CRÍTICA: Calcular SessionProfit de forma INCREMENTAL
        // Somar apenas os novos contratos que ainda não foram contabilizados
        decimal newProfit = 0m;
        int newContractCount = 0;
        
        _logger.LogInformation($"[DUAL] Adding new contracts to existing SessionProfit ({SessionProfit:F2}):");
        
        // Buscar apenas contratos não ativos da sessão atual que ainda não foram contabilizados
        var newContracts = ProfitTableEntries.Where(e => 
            !e.IsActive && 
            e.SessionId == CurrentDualSession && 
            !e.IsAccountedInSession // Nova flag para controlar se já foi contabilizado
        ).ToList();
        
        foreach (var entry in newContracts)
        {
            // Arredondar cada entrada individualmente para evitar acúmulo de erros de precisão
            decimal roundedProfit = Math.Round(entry.TotalProfitLoss, 2);
            newProfit += roundedProfit;
            newContractCount++;
            
            // Marcar como contabilizado para evitar dupla contagem
            entry.IsAccountedInSession = true;
            
            _logger.LogInformation($"[DUAL] NEW Contract {entry.RefId} (Session {entry.SessionId}): {entry.Contract} = {entry.TotalProfitLoss:F2} (rounded: {roundedProfit:F2})");
        }
        
        _logger.LogInformation($"[DUAL] {newContractCount} novos contratos processados, adicionando {newProfit:F2} ao SessionProfit");
        
        // CORREÇÃO: Somar incrementalmente ao SessionProfit existente
        SessionProfit = Math.Round(SessionProfit + newProfit, 2);
        
        // Atualizar _previousSessionProfit após mudança no SessionProfit
        _previousSessionProfit = SessionProfit;
        
        _logger.LogInformation($"[DUAL] SessionProfit updated to: {SessionProfit:F2} (was {SessionProfit - newProfit:F2} + {newProfit:F2})");
        _logger.LogInformation($"[DUAL] Updated _previousSessionProfit to: {_previousSessionProfit:F2}");
        
        // Atualizar prejuízo acumulado
        if (SessionProfit < 0)
        {
            _accumulatedLoss = Math.Abs(SessionProfit);
            _logger.LogInformation($"[DUAL] Prejuízo acumulado atualizado para: {_accumulatedLoss:F2}");
        }
        else
        {
            _accumulatedLoss = 0m;
            _logger.LogInformation($"[DUAL] Prejuízo recuperado, acumulado zerado");
        }
        
        // Determinar qual contrato perdeu na última rodada para próxima entrada
        if (_pendingDualContracts.Count >= 2)
        {
            var contract1Id = _pendingDualContracts[0];
            var contract2Id = _pendingDualContracts[1];
            
            var contract1Entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contract1Id);
            var contract2Entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contract2Id);
            
            if (contract1Entry != null && contract2Entry != null)
            {
                decimal contract1Result = contract1Entry.TotalProfitLoss;
                decimal contract2Result = contract2Entry.TotalProfitLoss;
                
                if (contract1Result > 0 && contract2Result <= 0)
                {
                    _losingContractTypeIndex = 1; // Contrato 2 perdeu
                    _logger.LogInformation($"[DUAL] Contract 1 WON: {contract1Result:F2}, Contract 2 LOST: {contract2Result:F2}");
                    

                }
                else if (contract2Result > 0 && contract1Result <= 0)
                {
                    _losingContractTypeIndex = 0; // Contrato 1 perdeu
                    _logger.LogInformation($"[DUAL] Contract 2 WON: {contract2Result:F2}, Contract 1 LOST: {contract1Result:F2}");
                    

                }
                else
                {
                    _losingContractTypeIndex = -1; // Ambos tiveram mesmo resultado
                    _logger.LogInformation($"[DUAL] Both contracts had same result: {contract1Result:F2}, {contract2Result:F2}");
                    

                }
            }
        }
        
        // Notificar UI
        OnPropertyChanged(nameof(SessionProfit));
    }

    private bool ShouldProcessDualLevel()
    {
        _completedDualContracts++;
        _logger.LogInformation($"[DUAL] Contracts completed: {_completedDualContracts}/2");
        
        // Só processar quando ambos os contratos finalizaram
        return _completedDualContracts >= 2;
    }

    private async Task ProcessDualLevelCompleteAsync()
    {
        _logger.LogInformation($"[DUAL] 🎯 ProcessDualLevelComplete STARTED - Level {CurrentDualLevel}, SessionProfit: {SessionProfit:F2}");
        Console.WriteLine($"[DUAL] 🎯 ProcessDualLevelComplete STARTED - Level {CurrentDualLevel}, SessionProfit: {SessionProfit:F2}");

        // LÓGICA DE CORREÇÃO FORÇADA REMOVIDA - ESTAVA DISTORCENDO RESULTADOS REAIS
        // A lógica anterior forçava resultados artificiais quando ambos contratos tinham o mesmo resultado
        // Isso alterava os valores reais de profit/loss, distorcendo completamente os resultados do trading
        // Os resultados reais dos contratos devem ser preservados, mesmo que ambos ganhem ou percam
        
        bool bothLost = _dualContract1Profit < 0 && _dualContract2Profit < 0;
        bool bothWon = _dualContract1Profit > 0 && _dualContract2Profit > 0;
        bool isFirstEntry = CurrentDualLevel == 0;
        
        if (bothLost || bothWon)
        {
            _logger.LogInformation($"[DUAL REAL RESULT] Ambos contratos com mesmo resultado - Contract1={_dualContract1Profit:F2}, Contract2={_dualContract2Profit:F2}");
            _logger.LogInformation($"[DUAL REAL RESULT] Preservando resultados reais - não aplicando correção artificial");
        }
        
        if (isFirstEntry)
        {
            _logger.LogInformation($"[DUAL REAL RESULT] Primeira entrada (Level 0) - Contract1={_dualContract1Profit:F2}, Contract2={_dualContract2Profit:F2}");
        }

        // LOG DETALHADO: Cálculo do SessionProfit
        var sessionProfitBefore = SessionProfit;
        var totalProfitBefore = TotalProfit;
        
        _logger.LogInformation($"[SESSION PROFIT] ===== CALCULANDO SESSION PROFIT =====");
        _logger.LogInformation($"[SESSION PROFIT] Contract 1 Profit: {_dualContract1Profit:F2}");
        _logger.LogInformation($"[SESSION PROFIT] Contract 2 Profit: {_dualContract2Profit:F2}");
        _logger.LogInformation($"[SESSION PROFIT] SessionProfit antes: {sessionProfitBefore:F2}");
        _logger.LogInformation($"[SESSION PROFIT] TotalProfit antes: {totalProfitBefore:F2}");
        
        // Cálculo incremental: somar apenas o resultado líquido da dupla recém-expirada
        // Garantir arredondamento a cada dupla para evitar drift
        decimal pairNet = Math.Round(_dualContract1Profit + _dualContract2Profit, 2);

        _logger.LogInformation($"[SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): {pairNet:F2}");
        _logger.LogInformation($"[SESSION PROFIT] 🧮 Cálculo: {_dualContract1Profit:F2} + {_dualContract2Profit:F2} = {pairNet:F2}");
        Console.WriteLine($"[SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): {pairNet:F2}");
        Console.WriteLine($"[SESSION PROFIT] 🧮 Cálculo: {_dualContract1Profit:F2} + {_dualContract2Profit:F2} = {pairNet:F2}");

        // Atualizar perdas acumuladas para o novo modo Dual
        var perdasAntes = DualPerdasAcumuladas;
        UpdateDualPerdasAcumuladas(pairNet);
        var perdasDepois = DualPerdasAcumuladas;
        _logger.LogInformation($"[NEW_DUAL] 💰 Perdas acumuladas - Antes: {perdasAntes:F2}, Depois: {perdasDepois:F2}, Mudança: {perdasDepois - perdasAntes:F2}");
        Console.WriteLine($"[NEW_DUAL] 💰 Perdas acumuladas - Antes: {perdasAntes:F2}, Depois: {perdasDepois:F2}, Mudança: {perdasDepois - perdasAntes:F2}");

        // Atualizar indicadores de quem perdeu para a próxima distribuição
        if (_dualContract1Profit > 0 && _dualContract2Profit <= 0)
        {
            _losingContractTypeIndex = 1; // contrato 2 perdeu
            _logger.LogInformation($"[SESSION PROFIT] Contract 1 ganhou, Contract 2 perdeu. LosingContractTypeIndex = 1");
        }
        else if (_dualContract2Profit > 0 && _dualContract1Profit <= 0)
        {
            _losingContractTypeIndex = 0; // contrato 1 perdeu
            _logger.LogInformation($"[SESSION PROFIT] Contract 2 ganhou, Contract 1 perdeu. LosingContractTypeIndex = 0");
        }
        else
        {
            _losingContractTypeIndex = -1; // empate/mesmo sinal
            _logger.LogInformation($"[SESSION PROFIT] Empate ou mesmo sinal. LosingContractTypeIndex = -1");
        }

        // Atualizar lucro da sessão com o resultado da dupla
        SessionProfit = Math.Round(SessionProfit + pairNet, 2);
        
        _logger.LogInformation($"[SESSION PROFIT] SessionProfit atualizado: {sessionProfitBefore:F2} + {pairNet:F2} = {SessionProfit:F2}");
        _logger.LogInformation($"[SESSION PROFIT] Diferença no SessionProfit: {SessionProfit - sessionProfitBefore:F2}");
        _logger.LogInformation($"[DUAL] Pair result: {_dualContract1Profit:F2} + {_dualContract2Profit:F2} = {pairNet:F2}; SessionProfit (after): {SessionProfit:F2}");
        
        // CORREÇÃO: Atualizar _previousSessionProfit após mudança no SessionProfit
        _previousSessionProfit = SessionProfit;
        _logger.LogInformation($"[DUAL] Updated _previousSessionProfit to: {_previousSessionProfit:F2}");
        
        // CORREÇÃO REMOVIDA: CalculateDualSessionProfit estava causando duplicação
        // O SessionProfit já foi calculado corretamente acima, não precisa recalcular
        _logger.LogInformation($"[DUAL] SessionProfit calculation completed - avoiding duplicate calculation");

        // Micro-metas: transferir Lucro Base para TotalProfit quando atingido
        if (IsDualEnabled)
        {
            ProcessDualMicroPayouts();
        }

        // A atualização do prejuízo acumulado será feita na lógica de vitória/derrota abaixo

        // Verificar se trading foi parado
        if (!IsTradingEnabled)
        {
            _logger.LogInformation("[DUAL] Trading disabled by STOP - halting dual execution");
            _isDualEntryPending = false;
            _completedDualContracts = 0;
            _dualContract1Completed = false;
            _dualContract2Completed = false;
            return;
        }

        // Resetar controle de contratos completados para próximo nível
        _completedDualContracts = 0;
        _isDualEntryPending = false;
        _dualContract1Completed = false;
        _dualContract2Completed = false;
        _dualContract1Profit = 0;
        _dualContract2Profit = 0;

        // CORREÇÃO CRÍTICA: Verificar se atingiu Take Profit e executar CompleteSession() SEMPRE
        // No modo dual, o alvo de parada é o campo "Lucro Alvo" (DualLucroAlvo) considerando TotalProfit acumulado
        if (SessionProfit >= DualLucroAlvo || (IsDualEnabled && _completedSessionsProfit >= DualLucroAlvo))
        {
            _logger.LogInformation($"[DUAL] Take profit atingido! Session profit: {SessionProfit:F2} >= Target: {DualLucroAlvo:F2}");
            _logger.LogInformation($"[DUAL] Executando CompleteSession() - sessão será finalizada com sucesso");
            CompleteSession();
            return;
        }
        else
        {
            // LOG DETALHADO: Rastrear quando sessões não encerram
            _logger.LogInformation($"[SESSION_END_DEBUG] Sessão NÃO encerrada - SessionProfit: {SessionProfit:F2}, Target: {DualLucroAlvo:F2}");
            _logger.LogInformation($"[SESSION_END_DEBUG] Diferença para target: {DualLucroAlvo - SessionProfit:F2}");
            _logger.LogInformation($"[SESSION_END_DEBUG] Nível atual: {CurrentDualLevel}/{DualLevel}, Sessão: {CurrentDualSession}/{DualSession}");
            
            // Verificar se há prejuízo significativo que deveria encerrar a sessão
            if (SessionProfit < -Math.Abs(DualTakeProfit))
            {
                _logger.LogWarning($"[SESSION_END_DEBUG] ⚠️ PREJUÍZO SIGNIFICATIVO DETECTADO: {SessionProfit:F2} (limite: -{Math.Abs(DualTakeProfit):F2})");
                _logger.LogWarning($"[SESSION_END_DEBUG] Sessão deveria ser encerrada por prejuízo, mas proteções podem estar impedindo");
            }
        }

        // CORREÇÃO: Level deve SEMPRE incrementar a cada entrada dupla, independente do resultado
        {
            // SEMPRE incrementar o level a cada entrada dupla completada
            CurrentDualLevel++;
            _logger.LogInformation($"[DUAL] ✅ Entrada dupla #{CurrentDualLevel} completada - Resultado líquido: {pairNet:F2}");

            if (pairNet < 0)
            {
                _logger.LogInformation($"[DUAL] 📉 PREJUÍZO na entrada #{CurrentDualLevel} - Acumulando perdas para recuperação");
                // Manter perdas acumuladas para próxima entrada
            }
            else
            {
                _logger.LogInformation($"[DUAL] 📈 LUCRO na entrada #{CurrentDualLevel} - Reduzindo perdas acumuladas");
                // Perdas são reduzidas automaticamente pelo UpdateDualPerdasAcumuladas
            }

            // Verificar se atingiu o nível máximo da sessão
            if (CurrentDualLevel >= DualLevel)
            {
                _logger.LogInformation($"[DUAL] 🎯 Nível máximo ({DualLevel}) atingido - Completando sessão atual");
                CurrentDualLevel = 0; // Reset para próxima sessão
                _accumulatedLoss = 0m;
                _losingContractTypeIndex = -1;

                // Incrementar sessão
                CurrentDualSession++;
                _logger.LogInformation($"[DUAL] 📊 Sessão incrementada para {CurrentDualSession}/{DualSession}");

                // Verificar se completou todas as sessões
                if (CurrentDualSession > DualSession)
                {
                    _logger.LogInformation($"[DUAL] 🏁 Todas as sessões completadas - Reiniciando ciclo dual");
                    // CORREÇÃO: Não desabilitar o modo dual, apenas reiniciar o ciclo
                    CurrentDualSession = 1; // Reiniciar para sessão 1
                    CurrentDualLevel = 0;   // Reiniciar para nível 0
                    _accumulatedLoss = 0m;  // Resetar perdas acumuladas
                    _losingContractTypeIndex = -1; // Resetar índice

                    _logger.LogInformation($"[DUAL] 🔄 Ciclo reiniciado - Sessão: {CurrentDualSession}/{DualSession}, Nível: {CurrentDualLevel}/{DualLevel}");
                    // Continuar trading sem parar
                }
            }
        }

        // CORREÇÃO: Não verificar limite de sessões aqui - isso é feito apenas quando uma sessão é completada
        // A verificação de sessões deve ser feita apenas no CompleteSession(), não a cada nível
        _logger.LogInformation($"[DUAL DEBUG] Continuing within session {CurrentDualSession}/{DualSession} at level {CurrentDualLevel}/{DualLevel}");

        // CORREÇÃO: Verificar se deve continuar automaticamente ou aguardar comando manual
        if (IsDualAutoMode)
        {
            // Modo automático: continuar para próximo nível automaticamente SEM DELAY
            _logger.LogInformation($"[DUAL] 🚀 Executing next dual entry FAST PATH (AUTO MODE)... (Level {CurrentDualLevel}/{DualLevel})");
            if (_isDualBuyInFlight) return;
            _isDualBuyInFlight = true;
            try
            {
                await BuyNextDualPairFastAsync();
            }
            finally
            {
                _isDualBuyInFlight = false;
            }
        }
        else
        {
            // Modo manual: aguardar próximo clique do usuário
            _logger.LogInformation($"[DUAL] ✋ Manual mode - waiting for user to click BUY for next dual entry (Level {CurrentDualLevel}/{DualLevel})");
            _isDualEntryPending = false; // Permitir que o usuário clique novamente
        }

        // CORREÇÃO: Eliminar delay - executar próxima entrada IMEDIATAMENTE
        // Não aguardar - executar instantaneamente para garantir velocidade máxima

        // Verificar se o trading ainda está ativo e reativar se necessário
        if (!IsTradingEnabled && IsDualEnabled)
        {
            _logger.LogInformation($"[DUAL] Trading foi desabilitado - reativando para continuar modo dual");
            IsTradingEnabled = true;
        }

        // Verificar se realmente não há contratos ativos ou pendentes
        bool noActiveContracts = _pendingDualContracts.Count == 0;
        bool noRecentActivity = !_isDualEntryPending;
        bool shouldRetry = noActiveContracts && noRecentActivity && IsDualEnabled && IsTradingEnabled && IsConnected;

        if (shouldRetry)
        {
            _logger.LogWarning("[ULTRA FAST WATCHDOG] No activity detected - immediate retry without delay");

            // OTIMIZAÇÃO CRÍTICA: Eliminar delay de verificação - retry imediato
            // Verificação imediata sem aguardar
            if (_pendingDualContracts.Count == 0 && !_isDualBuyInFlight && IsDualEnabled)
            {
                _logger.LogWarning("[ULTRA FAST WATCHDOG] Immediate retry - no pending contracts");
                _isDualBuyInFlight = true;
                try { await BuyNextDualPairFastAsync(); }
                finally { _isDualBuyInFlight = false; }
            }
            else
            {
                _logger.LogInformation("[ULTRA FAST WATCHDOG] Activity detected - canceling retry");
            }
        }
    }

    // Caminho rápido: calcula stakes, busca propostas em paralelo e compra imediatamente (sem validações pesadas)
    private async Task BuyNextDualPairFastAsync()
    {
        try
        {
            if (!IsConnected || !IsTradingEnabled || !IsDualEnabled)
                return;

            if (SelectedContractType == null || SelectedDualContractType == null)
                return;

            // Contabilizar nível da dupla atual (cada par conta 1 nível)
            _dualLevelsInCurrentGoal++;

            // Calcular rapidamente novas stakes com base no estado já atualizado
            var (stakeX, stakeY) = CalculateNewDualStakes();

            // Decidir quem recebe a stake maior
            bool firstHigher = (CurrentDualLevel == 0) ? true : (_losingContractTypeIndex == 0);
            decimal stake1 = firstHigher ? Math.Max(stakeX, stakeY) : Math.Min(stakeX, stakeY);
            decimal stake2 = firstHigher ? Math.Min(stakeX, stakeY) : Math.Max(stakeX, stakeY);

            // Preparar requests para operação ZERO-DELAY (proposta+compra em um passo)
            // Use prepared requests if available and fresh; otherwise build new
            var now = DateTimeOffset.UtcNow;
            bool usePrepared = _preparedDualReqTime.HasValue && (now - _preparedDualReqTime!.Value).TotalSeconds <= 3;

            var req1 = usePrepared && _preparedDualReq1 != null ? _preparedDualReq1! : new ProposalRequest
            {
                Stake = stake1,
                Symbol = SelectedActiveSymbol!.Symbol,
                ContractType = SelectedContractType.ContractType,
                Currency = "USD",
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null,
                LastDigitPrediction = IsDigitSelectionVisible ? SelectedDigit : null
            };

            var req2 = usePrepared && _preparedDualReq2 != null ? _preparedDualReq2! : new ProposalRequest
            {
                Stake = stake2,
                Symbol = SelectedActiveSymbol!.Symbol,
                ContractType = SelectedDualContractType.ContractType,
                Currency = "USD",
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null,
                LastDigitPrediction = IsDigitSelectionVisible ? SelectedDigit : null
            };

            // Preparar estado de pendência antes de enviar para evitar reentrâncias
            _isDualEntryPending = true;
            _pendingDualContracts.Clear();
            _completedDualContracts = 0;

            // Tentar usar proposal streams quentes se disponíveis
            var pStream1 = !string.IsNullOrEmpty(_dualPropKey1) ? _derivApiService.GetLatestProposalFromStream(_dualPropKey1) : null;
            var pStream2 = !string.IsNullOrEmpty(_dualPropKey2) ? _derivApiService.GetLatestProposalFromStream(_dualPropKey2) : null;

            BuyResponse? buy1 = null;
            BuyResponse? buy2 = null;

            if (pStream1?.Proposal?.Id != null && pStream2?.Proposal?.Id != null)
            {
                // OTIMIZAÇÃO CRÍTICA: Usar método ultra-rápido para compra dual simultânea
                _logger.LogInformation($"[ULTRA FAST] Executing ultra-fast dual buy - maximum speed mode");

                // Usar método otimizado para compra dual simultânea
                var dualResult = await _derivApiService.BuyDualContractsUltraFastAsync(
                    pStream1.Proposal.Id, pStream1.Proposal.AskPrice,
                    pStream2.Proposal.Id, pStream2.Proposal.AskPrice);

                buy1 = dualResult.buy1;
                buy2 = dualResult.buy2;
            }
            else
            {
                // Fallback: Buy instant pool
                var tcs1 = new TaskCompletionSource<BuyResponse?>();
                var tcs2 = new TaskCompletionSource<BuyResponse?>();
                _derivApiService.BuyInstantMarketAsync(req1, (buy) => tcs1.TrySetResult(buy));
                _derivApiService.BuyInstantMarketAsync(req2, (buy) => tcs2.TrySetResult(buy));
                var res = await Task.WhenAll(tcs1.Task, tcs2.Task);
                buy1 = res[0];
                buy2 = res[1];
            }

            // Registra contexto de controle após sucesso das compras
            _isDualEntryPending = true;
            _pendingDualContracts.Clear();
            _completedDualContracts = 0;

            // Não adicionamos linhas na tabela aqui; aguardamos OnContractPurchased (fonte autoritativa)

            // CORREÇÃO: Reduzir delay para acelerar exibição em tempo real
            await Task.Delay(50);

            // Parar subscriptions antigas após enviar compras
            if (!string.IsNullOrEmpty(_dualPropKey1)) _derivApiService.StopProposalSubscription(_dualPropKey1);
            if (!string.IsNullOrEmpty(_dualPropKey2)) _derivApiService.StopProposalSubscription(_dualPropKey2);
            _dualPropKey1 = _dualPropKey2 = null;

            // OTIMIZAÇÃO CRÍTICA: Eliminar delay de verificação - executar fallback imediatamente se necessário
            bool streamBuysConfirmed = (buy1?.Buy != null && buy2?.Buy != null);
            if (!streamBuysConfirmed && _pendingDualContracts.Count == 0 && IsDualEnabled && IsConnected && IsTradingEnabled)
            {
                _logger.LogWarning("[ULTRA FAST] Stream buy failed - executing immediate fallback");
                try
                {
                    // Executar fallback imediatamente sem delay
                    var tcs1 = new TaskCompletionSource<(ProposalResponse?, BuyResponse?)>();
                    var tcs2 = new TaskCompletionSource<(ProposalResponse?, BuyResponse?)>();
                    _derivApiService.GetFastProposalAndBuyAsync(req1, (prop, buy) => tcs1.TrySetResult((prop, buy)));
                    _derivApiService.GetFastProposalAndBuyAsync(req2, (prop, buy) => tcs2.TrySetResult((prop, buy)));
                    var _ = Task.WhenAll(tcs1.Task, tcs2.Task); // resultados entrarão via OnContractPurchased
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[ULTRA FAST] Immediate fallback failed");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[FAST DUAL] Error on fast buy path");
        }
    }

    // OTIMIZAÇÃO CRÍTICA: Método de alinhamento removido para eliminar delays
    // O alinhamento de 50ms estava causando atraso desnecessário
    private async Task TryAlignEntryWithLastExitAsync()
    {
        // ULTRA FAST MODE: Sem alinhamento - execução imediata
        _logger.LogInformation($"[ULTRA FAST] Skipping alignment - immediate execution for maximum speed");
        await Task.CompletedTask; // Retorno imediato
    }

    // Preparação de propostas para o próximo par durante os últimos segundos do contrato atual
    private ProposalRequest? _preparedDualReq1;
    private ProposalRequest? _preparedDualReq2;
    private DateTimeOffset? _preparedDualReqTime;
    private string? _dualPropKey1;
    private string? _dualPropKey2;

    private void PrepareNextDualRequests()
    {
        try
        {
            if (!IsDualEnabled || SelectedContractType == null || SelectedDualContractType == null || SelectedActiveSymbol == null)
                return;

            var (stakeX, stakeY) = CalculateNewDualStakes();
            // Preparar as duas distribuições (maior/menor), já que só saberemos quem perdeu ao expirar
            decimal higher = Math.Max(stakeX, stakeY);
            decimal lower = Math.Min(stakeX, stakeY);

            _preparedDualReq1 = new ProposalRequest
            {
                Stake = higher,
                Symbol = SelectedActiveSymbol.Symbol,
                ContractType = SelectedContractType.ContractType,
                Currency = "USD",
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null,
                LastDigitPrediction = IsDigitSelectionVisible ? SelectedDigit : null
            };

            _preparedDualReq2 = new ProposalRequest
            {
                Stake = lower,
                Symbol = SelectedActiveSymbol.Symbol,
                ContractType = SelectedDualContractType.ContractType,
                Currency = "USD",
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null,
                LastDigitPrediction = IsDigitSelectionVisible ? SelectedDigit : null
            };

            _preparedDualReqTime = DateTimeOffset.UtcNow;
            _logger.LogInformation("[DUAL PREP] Próximas requests preparadas às {Time:HH:mm:ss.fff}", _preparedDualReqTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL PREP] Erro ao preparar requests para próxima dupla");
        }
    }

    private async void StartDualProposalSubscriptions()
    {
        try
        {
            if (_preparedDualReq1 == null || _preparedDualReq2 == null) return;
            _dualPropKey1 = $"dual_{SelectedContractType?.ContractType}_high";
            _dualPropKey2 = $"dual_{SelectedDualContractType?.ContractType}_low";
            await _derivApiService.StartProposalSubscriptionAsync(_preparedDualReq1, _dualPropKey1);
            await _derivApiService.StartProposalSubscriptionAsync(_preparedDualReq2, _dualPropKey2);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "[DUAL PREP] Falha ao iniciar subscriptions de propostas");
        }
    }

    private void CompleteSession()
    {
        _logger.LogInformation($"[DUAL] Attempting to complete session {CurrentDualSession}. Session profit: {SessionProfit:F2}");

        // PROTEÇÃO: Uma sessão dual NUNCA deve encerrar com prejuízo!
        if (SessionProfit < 0)
        {
            _logger.LogWarning($"[DUAL] BLOCKING session completion due to LOSS! SessionProfit: {SessionProfit:F2}");
            Console.WriteLine($"[DUAL PROTECTION] Session completion BLOCKED - Loss detected: {SessionProfit:F2}");
            Console.WriteLine($"[DUAL PROTECTION] System will continue trading until profit is achieved!");
            
            // LOG DETALHADO: Rastrear bloqueios por prejuízo
            _logger.LogError($"[COMPLETE_SESSION_DEBUG] 🛑 ENCERRAMENTO BLOQUEADO POR PREJUÍZO");
            _logger.LogError($"[COMPLETE_SESSION_DEBUG] SessionProfit atual: {SessionProfit:F2}");
            _logger.LogError($"[COMPLETE_SESSION_DEBUG] Target esperado: {DualTakeProfit:F2}");
            _logger.LogError($"[COMPLETE_SESSION_DEBUG] Nível: {CurrentDualLevel}/{DualLevel}, Sessão: {CurrentDualSession}/{DualSession}");
            _logger.LogError($"[COMPLETE_SESSION_DEBUG] ⚠️ ESTA PROTEÇÃO PODE ESTAR CAUSANDO O PROBLEMA RELATADO!");
            _logger.LogError($"[COMPLETE_SESSION_DEBUG] Sistema continuará tentando até atingir lucro, mas pode estar perdendo dinheiro");
            
            // Não encerrar - retornar para continuar tentando
            return;
        }
        
        // PROTEÇÃO: Não encerrar abaixo do target sob nenhuma condição (regra estrita)
        decimal sessionTarget = IsDualEnabled ? DualLucroAlvo : DualTakeProfit;
        if (SessionProfit < sessionTarget)
        {
            _logger.LogWarning($"[DUAL] Session profit ({SessionProfit:F2}) below target ({sessionTarget:F2}) - continuing to next level");
            Console.WriteLine($"[DUAL PROTECTION] Session profit {SessionProfit:F2} below target {sessionTarget:F2} - continuing...");
            
            // LOG DETALHADO: Rastrear bloqueios por target não atingido
            _logger.LogWarning($"[COMPLETE_SESSION_DEBUG] 🎯 ENCERRAMENTO BLOQUEADO - TARGET NÃO ATINGIDO");
            _logger.LogWarning($"[COMPLETE_SESSION_DEBUG] SessionProfit atual: {SessionProfit:F2}");
            _logger.LogWarning($"[COMPLETE_SESSION_DEBUG] Target necessário: {sessionTarget:F2}");
            _logger.LogWarning($"[COMPLETE_SESSION_DEBUG] Diferença: {sessionTarget - SessionProfit:F2}");
            _logger.LogWarning($"[COMPLETE_SESSION_DEBUG] Nível: {CurrentDualLevel}/{DualLevel}, Sessão: {CurrentDualSession}/{DualSession}");
            
            if (SessionProfit > 0 && SessionProfit < DualTakeProfit)
            {
                _logger.LogInformation($"[COMPLETE_SESSION_DEBUG] ✅ Há lucro ({SessionProfit:F2}), mas abaixo do target - continuando");
            }
            else if (SessionProfit < 0)
            {
                _logger.LogError($"[COMPLETE_SESSION_DEBUG] ❌ Prejuízo detectado: {SessionProfit:F2} - sistema forçado a continuar");
            }
            
            return; // Não encerrar - continuar tentando
        }

        // LOG DETALHADO: Transferência SessionProfit -> CompletedSessionsProfit
        decimal previousTotalProfit = _completedSessionsProfit;
        // No modo dual com micro-metas, o acumulado já foi transferido gradualmente
        // Evitar dupla transferência ao encerrar a sessão
        decimal currentSessionProfit = IsDualEnabled ? 0m : SessionProfit;
        var balanceBeforeTransfer = CalculateExpectedBalance();
        var activeExposureBeforeTransfer = ActiveExposure;
        
        _logger.LogInformation($"[TRANSFER] ===== TRANSFERINDO SESSION PROFIT =====");
        _logger.LogInformation($"[TRANSFER] Session ID: {CurrentDualSession}");
        _logger.LogInformation($"[TRANSFER] SessionProfit atual: {currentSessionProfit:F2}");
        _logger.LogInformation($"[TRANSFER] CompletedSessionsProfit antes: {previousTotalProfit:F2}");
        _logger.LogInformation($"[TRANSFER] Saldo antes da transferência: {balanceBeforeTransfer:F2}");
        _logger.LogInformation($"[TRANSFER] Active Exposure antes: {activeExposureBeforeTransfer:F2}");
        
        // CORREÇÃO: Adicionar SessionProfit ao CompletedSessionsProfit com arredondamento consistente
        _completedSessionsProfit = Math.Round(previousTotalProfit + currentSessionProfit, 2);
        
        var balanceAfterTransfer = CalculateExpectedBalance();
        var activeExposureAfterTransfer = ActiveExposure;
        
        _logger.LogInformation($"[TRANSFER] CompletedSessionsProfit depois: {_completedSessionsProfit:F2}");
        _logger.LogInformation($"[TRANSFER] Diferença CompletedSessionsProfit: {_completedSessionsProfit - previousTotalProfit:F2}");
        _logger.LogInformation($"[TRANSFER] Saldo após transferência: {balanceAfterTransfer:F2}");
        _logger.LogInformation($"[TRANSFER] Active Exposure após: {activeExposureAfterTransfer:F2}");
        _logger.LogInformation($"[TRANSFER] Diferença no saldo: {balanceAfterTransfer - balanceBeforeTransfer:F2}");
        
        // Logs detalhados para debugging
        Console.WriteLine($"[SESSION COMPLETE] Session {CurrentDualSession} completed with PROFIT: {currentSessionProfit:F2}");
        Console.WriteLine($"[SESSION COMPLETE] CompletedSessionsProfit updated: {previousTotalProfit:F2} + {currentSessionProfit:F2} = {_completedSessionsProfit:F2}");
        
        _logger.LogInformation($"[DUAL] Session {CurrentDualSession} completed. SessionProfit: {currentSessionProfit:F2}, CompletedSessionsProfit: {previousTotalProfit:F2} -> {_completedSessionsProfit:F2}");
        _logger.LogInformation($"[BALANCE_UPDATE] Before session completion - InitialBalance: {InitialBalance:F2}, Balance: {Balance:F2}");
        
        // LOG DETALHADO: Reset do SessionProfit
        _logger.LogInformation($"[TRANSFER] ===== RESETANDO SESSION PROFIT =====");
        _logger.LogInformation($"[TRANSFER] SessionProfit antes do reset: {SessionProfit:F2}");
        
        // Resetar SessionProfit para nova sessão
        SessionProfit = 0m;
        _previousSessionProfit = 0m; // Reset histórico de SessionProfit para nova sessão
        
        _logger.LogInformation($"[TRANSFER] SessionProfit após reset: {SessionProfit:F2}");
        _logger.LogInformation($"[TRANSFER] _previousSessionProfit após reset: {_previousSessionProfit:F2}");
        _logger.LogInformation($"[BALANCE_UPDATE] After session completion - SessionProfit reset to 0, CompletedSessionsProfit: {_completedSessionsProfit:F2}");
        
        // Verificar consistência após completar sessão
        ValidateBalanceConsistency(Balance, "Session Completion");
        
        // RESET DOS ESTADOS PARA NOVA SESSÃO
        _logger.LogInformation($"[DUAL] Resetando estados para nova sessão");
        
        _accumulatedLoss = 0m; // Resetar prejuízo acumulado normal
        _logger.LogInformation($"[DUAL] Accumulated loss reset to 0");
        
        _losingContractTypeIndex = -1; // Resetar índice do contrato que perdeu
        CurrentDualLevel = 0; // Resetar nível para primeira rodada
        
        _logger.LogInformation($"[DUAL] Estados resetados - CurrentDualLevel: {CurrentDualLevel}, AccumulatedLoss: {_accumulatedLoss:F2}");
        
        Console.WriteLine($"[SESSION RESET] SessionProfit reset to 0 for next session. CompletedSessionsProfit preserved: {_completedSessionsProfit:F2}");
        
        // Notificar UI sobre mudanças
        Application.Current.Dispatcher.Invoke(() =>
        {
            OnPropertyChanged(nameof(TotalProfit));
            OnPropertyChanged(nameof(SessionProfit));
        });

        // Verificar se deve continuar com próxima sessão
        if (CurrentDualSession < DualSession)
        {
            StartNewSession();
        }
        else
        {
            // Todas as sessões completadas - reiniciar ciclo
            _logger.LogInformation($"[DUAL] All {DualSession} sessions completed. Final Total Profit: {TotalProfit:F2}");
            _logger.LogInformation($"[DUAL] 🔄 Reiniciando ciclo dual para continuar trading");

            // Reiniciar ciclo sem desabilitar o modo dual
            CurrentDualSession = 1;
            CurrentDualLevel = 0;
            _accumulatedLoss = 0m;
            _losingContractTypeIndex = -1;
            _isDualEntryPending = false;
            _pendingDualContracts.Clear();
            _completedDualContracts = 0;
            _isProcessingDualCompletion = false;

            // Resetar controles dual específicos
            _dualContract1Completed = false;
            _dualContract2Completed = false;
            _dualContract1Profit = 0;
            _dualContract2Profit = 0;

            _logger.LogInformation($"[DUAL] 🚀 Iniciando nova entrada dual após reiniciar ciclo");

            // Iniciar nova entrada dual
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000); // Pequena pausa para estabilizar
                await ExecuteDualEntryCommand();
            });
        }
    }
    private void StartNewSession()
    {
        _logger.LogInformation($"[DUAL] Starting new session {CurrentDualSession + 1}");

        // CORREÇÃO: CompletedSessionsProfit já preserva valores de sessões anteriores com arredondamento
        decimal preservedCompleted = Math.Round(_completedSessionsProfit, 2);
        _completedSessionsProfit = preservedCompleted; // Garantir arredondamento consistente
        Console.WriteLine($"[SESSION TRANSITION] CompletedSessionsProfit preserved: {preservedCompleted:F2}");
        
        // Limpar tabela de profit da sessão anterior
        Application.Current.Dispatcher.Invoke(() =>
        {
            ProfitTableEntries.Clear();
        });
        
        // CompletedSessionsProfit permanece inalterado (não afetado pela limpeza da tabela)
        Console.WriteLine($"[SESSION TRANSITION] CompletedSessionsProfit maintained: {_completedSessionsProfit:F2} after clearing table");
        _logger.LogInformation($"[DUAL] CompletedSessionsProfit preserved across sessions: {_completedSessionsProfit:F2}");

        // Resetar variáveis da sessão
        CurrentDualSession++;
        CurrentDualLevel = 0;
        // SessionProfit já foi zerado em CompleteSession() após transferir para CompletedSessionsProfit
        _isDualEntryPending = false;
        _pendingDualContracts.Clear();
        _completedDualContracts = 0;
        
        // Resetar novos campos do modo dual
        _losingContractTypeIndex = -1;
        _accumulatedLoss = 0m;
        
        // Resetar controles dual específicos
        _dualContract1Completed = false;
        _dualContract2Completed = false;
        _dualContract1Profit = 0;
        _dualContract2Profit = 0;

        // Iniciar primeira entrada da nova sessão
        _ = Task.Run(async () =>
        {
            await ExecuteDualEntryCommand();
        });
    }

    private void ResetDualMode()
    {
        _logger.LogInformation("[DUAL] 🔄 Resetando modo dual para reiniciar ciclo");

        CurrentDualLevel = 0;
        MaxLevel = DualLevel; // Manter MaxLevel configurado
        CurrentDualSession = 1; // Reiniciar para sessão 1
        SessionProfit = 0;
        _previousSessionProfit = 0m; // Reset histórico de SessionProfit
        _isDualEntryPending = false;
        _pendingDualContracts.Clear();
        _completedDualContracts = 0;
        _isProcessingDualCompletion = false;

        // Resetar novos campos do modo dual
        _losingContractTypeIndex = -1;
        _accumulatedLoss = 0m;

        // Resetar controles dual específicos
        _dualContract1Completed = false;
        _dualContract2Completed = false;
        _dualContract1Profit = 0;
        _dualContract2Profit = 0;

        _logger.LogInformation("[DUAL] Mode reset completed - ready for new cycle");

        // CORREÇÃO: Não desabilitar o modo dual, apenas resetar para novo ciclo
        // IsDualEnabled permanece true para continuar o trading

        // Iniciar nova entrada dual após reset
        if (IsDualEnabled && IsTradingEnabled)
        {
            _logger.LogInformation("[DUAL] 🚀 Iniciando nova entrada dual após reset");
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000); // Pequena pausa para estabilizar
                await ExecuteDualEntryCommand();
            });
        }
    }

    // Override do StakeAmount para integrar com Martingale
    private string _stakeAmount = "1.00";
    public string StakeAmount
    {
        get => _stakeAmount;
        set
        {
            // Replace commas with dots for consistent decimal handling
            var normalizedInput = value.Replace(',', '.');

            // Allow empty field without enforcing minimum
            if (string.IsNullOrWhiteSpace(normalizedInput))
            {
                _stakeAmount = string.Empty;
                _stake = 0m;
                OnPropertyChanged();
                OnPropertyChanged(nameof(Stake));
                CalculateNextStake();
                // Do not calculate proposal if field is empty
                return;
            }

            // Keep raw input while editing
            _stakeAmount = normalizedInput;
            OnPropertyChanged();

            // Store raw value without minimum validation during editing
            if (decimal.TryParse(normalizedInput, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal stakeValue))
            {
                // Use internal setter to bypass minimum validation during editing
                SetStakeRaw(stakeValue);
            }

            // Update initial stake if martingale is enabled and this is a manual change
            if (IsMartingaleEnabled && decimal.TryParse(normalizedInput, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal stake))
            {
                if (CurrentMartingaleLevel == 0)
                {
                    InitialStakeAmount = stake;
                }
            }

            CalculateNextStake();
            CalculateProposalAsync();
        }
    }

    // Profit Table Methods (SOLID: Single Responsibility)
    private void AddProfitTableEntry(BuyContract buyContract)
    {
        AddProfitTableEntry(buyContract, null);
    }

    private void AddProfitTableEntry(BuyContract buyContract, string? customContractName)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            // Use only API-provided entry_tick_time and entry_tick. Do not fall back to local purchase_time or current tick.
            DateTime? entrySpotTime = null;
            if (buyContract.EntryTickTime.HasValue && buyContract.EntryTickTime.Value > 0)
            {
                entrySpotTime = DateTimeOffset.FromUnixTimeSeconds(buyContract.EntryTickTime.Value).UtcDateTime;
            }

            // Determine duration display from longcode when possible (robust regex parsing)
            string durationDisplay = GetDurationDisplayText();
            try
            {
                if (!string.IsNullOrEmpty(buyContract.LongCode))
                {
                    // Look for patterns like '1 tick', '5 ticks', '30 sec', '1 second'
                    var lc = buyContract.LongCode;
                    var match = System.Text.RegularExpressions.Regex.Match(lc, "(\\d+)\\s*(tick|ticks|second|seconds|sec|minute|minutes|min|hour|hours|hr)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var number = match.Groups[1].Value;
                        var unit = match.Groups[2].Value.ToLowerInvariant();
                        // Normalize unit
                        unit = unit switch
                        {
                            "tick" or "ticks" => (number == "1") ? "tick" : "ticks",
                            "second" or "seconds" or "sec" => (number == "1") ? "sec" : "secs",
                            "minute" or "minutes" or "min" => (number == "1") ? "min" : "mins",
                            "hour" or "hours" or "hr" => (number == "1") ? "hr" : "hrs",
                            _ => unit
                        };
                        durationDisplay = $"{number} {unit}";
                    }
                }
            }
            catch { }

            // LOG DETALHADO: Estado antes de adicionar entrada à tabela
            var balanceBeforeAdd = CalculateExpectedBalance();
            var activeExposureBeforeAdd = ActiveExposure;
            var entriesCountBefore = ProfitTableEntries.Count;
            
            var entry = new ProfitTableEntry
            {
                RefId = buyContract.ContractId.ToString(),
                Contract = customContractName ?? GetContractTypeDisplayName(),
                Duration = durationDisplay,
                EntrySpot = entrySpotTime, // null if API didn't provide entry_tick_time
                ExitSpot = null, // CORREÇÃO: Exit spot deve ficar null até o contrato ser finalizado
                Stake = buyContract.BuyPrice,
                Payout = buyContract.Payout,
                // API official entry tick only. Leave null when not provided by API.
                EntryPrice = buyContract.EntryTick,
                ExitPrice = null, // CORREÇÃO: Exit price deve ficar null até o contrato ser finalizado
                IsActive = true,
                SessionId = CurrentDualSession // CORREÇÃO: Associar entrada à sessão atual
            };

            // Update Max Stake tracking
            UpdateMaxStake(buyContract.BuyPrice);

            ProfitTableEntries.Insert(0, entry); // Add to top of list

            // CORREÇÃO: Forçar atualização da UI em tempo real (já estamos no Dispatcher)
            OnPropertyChanged(nameof(ProfitTableEntries));
            OnPropertyChanged(nameof(ActiveExposure));
            OnPropertyChanged(nameof(TotalProfit));
            OnPropertyChanged(nameof(SessionProfit));

            // Atualizar MaxStake baseado na tabela
            UpdateMaxStakeFromTable();

            // CORREÇÃO: Forçar recálculo completo do MaxStake
            RecalculateMaxStakeFromTable();

            // LOG DETALHADO: Impacto da adição da entrada
            var balanceAfterAdd = CalculateExpectedBalance();
            var activeExposureAfterAdd = ActiveExposure;
            var entriesCountAfter = ProfitTableEntries.Count;
            
            _logger.LogInformation($"[TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====");
            _logger.LogInformation($"[TABLE ADD] Contract ID: {entry.RefId}");
            _logger.LogInformation($"[TABLE ADD] Contract Type: {entry.Contract}");
            _logger.LogInformation($"[TABLE ADD] Stake: {entry.Stake:F2}");
            _logger.LogInformation($"[TABLE ADD] Payout: {entry.Payout:F2}");
            _logger.LogInformation($"[TABLE ADD] Entry Price: {entry.EntryPrice:F4}");
            _logger.LogInformation($"[TABLE ADD] Session ID: {entry.SessionId}");
            _logger.LogInformation($"[TABLE ADD] Is Active: {entry.IsActive}");
            _logger.LogInformation($"[TABLE ADD] Entries count: {entriesCountBefore} -> {entriesCountAfter}");
            _logger.LogInformation($"[TABLE ADD] Saldo: {balanceBeforeAdd:F2} -> {balanceAfterAdd:F2}");
            _logger.LogInformation($"[TABLE ADD] Active Exposure: {activeExposureBeforeAdd:F2} -> {activeExposureAfterAdd:F2}");
            _logger.LogInformation($"[TABLE ADD] Diferença Active Exposure: {activeExposureAfterAdd - activeExposureBeforeAdd:F2}");
            
            // Debug: Verify immediately after adding entry
            Console.WriteLine($"[TABLE DEBUG] Entry added - RefId: {entry.RefId}, P/L: {entry.TotalProfitLoss:F2}");
            VerifyTotalProfitCalculation();

            // Keep only last 50 entries to avoid memory issues
            while (ProfitTableEntries.Count > 50)
            {
                ProfitTableEntries.RemoveAt(ProfitTableEntries.Count - 1);
            }
        });
    }

    private string GetContractTypeDisplayName()
    {
        if (SelectedContractType != null)
        {
            return SelectedContractType.ContractDisplay;
        }
        return "Unknown";
    }

    private string GetDurationDisplayText()
    {
        if (!string.IsNullOrEmpty(DurationValue.ToString()) && !string.IsNullOrEmpty(DurationUnit))
        {
            var unitDisplay = DurationUnit switch
            {
                "t" => "ticks",
                "s" => "sec",
                "m" => "min",
                "h" => "hr",
                "d" => "day",
                _ => DurationUnit
            };
            return $"{DurationValue} {unitDisplay}";
        }
        return "---";
    }

    private void UpdateProfitTableEntry(string contractId, decimal currentPrice, decimal? profit = null, bool? isFinished = null)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId);
            if (entry != null)
            {
                entry.CurrentPrice = currentPrice;

                if (profit.HasValue)
                {
                    // CORREÇÃO: Arredondar o profit na entrada para evitar problemas de precisão
                    entry.TotalProfitLoss = Math.Round(profit.Value, 2);
                }

                if (isFinished.HasValue && isFinished.Value)
                {
                    // CORREÇÃO CRÍTICA: NÃO marcar IsActive = false aqui!
                    // Deixar OnContractFinished fazer isso para evitar problemas de busca
                    // entry.IsActive = false; // REMOVIDO - causa problema na busca do OnContractFinished

                    // CORREÇÃO: NÃO definir ExitPrice aqui - deixar OnContractFinished fazer isso
                    // para evitar que Exit Spot apareça prematuramente
                    // entry.ExitPrice = currentPrice; // REMOVIDO

                    _logger.LogInformation($"[CONTRACT_STATUS] Contract {contractId} marked as finished in UpdateProfitTableEntry, but IsActive remains true for OnContractFinished");
                }
            }
        });
    }

    private void UpdateActiveProfitTableEntries(decimal currentPrice)
    {
        bool hasActiveEntries = false;
        foreach (var entry in ProfitTableEntries.Where(e => e.IsActive))
        {
            entry.CurrentPrice = currentPrice;
            hasActiveEntries = true;
        }

        // CORREÇÃO: Forçar atualização da UI se há entradas ativas
        if (hasActiveEntries)
        {
            OnPropertyChanged(nameof(ProfitTableEntries));
            OnPropertyChanged(nameof(ActiveExposure));
            OnPropertyChanged(nameof(SessionProfit));
        }
    }

    private void OnContractFinished(string contractId, decimal profit, decimal exitPrice, DateTime exitTime)
    {
        // CORREÇÃO: Verificar se Application.Current não é null (pode ser null durante shutdown)
        if (Application.Current?.Dispatcher == null)
        {
            _logger.LogWarning($"[CONTRACT FINISHED] Application shutting down - skipping contract {contractId} finalization");
            return;
        }

        Application.Current.Dispatcher.Invoke(() =>
        {
            // Memorizar último exit para tentativa de alinhamento do próximo entry
            _lastExitPrice = exitPrice;
            _lastExitTime = exitTime.ToUniversalTime();

            // CORREÇÃO CRÍTICA: Buscar contrato sem filtro IsActive primeiro
            // para lidar com contratos que completaram muito rapidamente
            _logger.LogInformation($"[CONTRACT FINISHED] Searching for contract {contractId} in table with {ProfitTableEntries.Count} entries");

            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId);
            if (entry == null)
            {
                // Se não encontrou, tentar buscar apenas por IsActive
                _logger.LogWarning($"[CONTRACT FINISHED] Contract {contractId} not found without IsActive filter, trying with IsActive=true");
                entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId && e.IsActive);

                if (entry == null)
                {
                    _logger.LogError($"[CONTRACT FINISHED] Contract {contractId} not found in table at all!");
                    // Debug: Listar todos os contratos na tabela
                    foreach (var e in ProfitTableEntries.Take(5))
                    {
                        _logger.LogError($"[CONTRACT FINISHED DEBUG] Table entry: {e.RefId}, IsActive: {e.IsActive}");
                    }
                }
            }
            else
            {
                _logger.LogInformation($"[CONTRACT FINISHED] Found contract {contractId} in table, IsActive: {entry.IsActive}");
            }

            if (entry != null)
            {
                // CORREÇÃO: Arredondar o profit para garantir precisão correta
                var roundedProfit = Math.Round(profit, 2);
                entry.TotalProfitLoss = roundedProfit;
                entry.ExitPrice = exitPrice; // API spot price at exit
                // Ensure ExitSpot is stored as UTC (API official time may be local or UTC)
                entry.ExitSpot = exitTime.ToUniversalTime(); // store as GMT
                entry.IsActive = false;
                
                // LOG CRÍTICO: Confirmar que IsActive foi definido como false
                _logger.LogInformation($"[CONTRACT_STATUS] Contract {contractId} IsActive set to FALSE - Contract finalized successfully");

                // CORREÇÃO: Forçar atualização da UI quando contrato é finalizado
                OnPropertyChanged(nameof(ProfitTableEntries));
                OnPropertyChanged(nameof(ActiveExposure));
                OnPropertyChanged(nameof(SessionProfit));
                OnPropertyChanged(nameof(TotalProfit));
                OnPropertyChanged(nameof(MaxStake));

                // LOGS DETALHADOS PARA RASTREAMENTO DE TRANSAÇÕES
                _logger.LogInformation($"[TRANSACTION] Contract {contractId} FINISHED");
                _logger.LogInformation($"[TRANSACTION] - Contract Type: {entry.Contract}");
                _logger.LogInformation($"[TRANSACTION] - Stake: {entry.Stake:F2}");
                _logger.LogInformation($"[TRANSACTION] - Payout: {entry.Payout:F2}");
                _logger.LogInformation($"[TRANSACTION] - Entry Price: {entry.EntryPrice:F4}");
                _logger.LogInformation($"[TRANSACTION] - Exit Price: {exitPrice:F4}");
                _logger.LogInformation($"[TRANSACTION] - Raw Profit: {profit:F6}");
                _logger.LogInformation($"[TRANSACTION] - Rounded Profit: {roundedProfit:F2}");
                _logger.LogInformation($"[TRANSACTION] - Result: {(roundedProfit > 0 ? "WIN" : "LOSS")}");
                _logger.LogInformation($"[TRANSACTION] - Session ID: {entry.SessionId}");
                _logger.LogInformation($"[TRANSACTION] - Is Dual Mode: {entry.IsDual}");
                
                // Log do estado do saldo antes e depois
                var balanceBefore = CalculateExpectedBalance();
                _logger.LogInformation($"[BALANCE] Before transaction: {balanceBefore:F2}");
                
                _logger.LogInformation($"Profit Table updated for contract {contractId}: Profit={roundedProfit:F2}, ExitPrice={exitPrice}, ExitTime={exitTime:HH:mm:ss}");
                
                // Atualizar contador de perdas consecutivas para pausa automática
                UpdateConsecutiveLossesForPause(profit > 0);
                
                // Atualizar sistema de recuperação escalonada
                UpdateRecoverySystem(profit > 0, profit);
                
                // CORREÇÃO: Para modo dual, não chamar UpdateTotalProfitFromTable automaticamente
                // O SessionProfit já é atualizado corretamente e TotalProfit = _completedSessionsProfit + SessionProfit
                if (!IsDualEnabled)
                {
                    // Para operações não-dual, atualizar TotalProfit automaticamente
                    var totalProfitBeforeUpdate = TotalProfit;
                    var sessionProfitBeforeUpdate = SessionProfit;
                    _logger.LogInformation($"[TOTAL_PROFIT_DEBUG] ANTES UpdateTotalProfitFromTable - TotalProfit: {totalProfitBeforeUpdate:F2}, SessionProfit: {sessionProfitBeforeUpdate:F2}");

                    UpdateTotalProfitFromTable();

                    var totalProfitAfterUpdate = TotalProfit;
                    var sessionProfitAfterUpdate = SessionProfit;
                    _logger.LogInformation($"[TOTAL_PROFIT_DEBUG] DEPOIS UpdateTotalProfitFromTable - TotalProfit: {totalProfitAfterUpdate:F2}, SessionProfit: {sessionProfitAfterUpdate:F2}");
                    _logger.LogInformation($"[TOTAL_PROFIT_DEBUG] Mudança TotalProfit: {totalProfitAfterUpdate - totalProfitBeforeUpdate:F2}, Mudança SessionProfit: {sessionProfitAfterUpdate - sessionProfitBeforeUpdate:F2}");
                }
                else
                {
                    _logger.LogInformation($"[TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: {TotalProfit:F2} (Completed: {_completedSessionsProfit:F2} + Session: {SessionProfit:F2})");
                }

                Console.WriteLine($"[CONTRACT FINISHED] Contract {contractId} profit: {profit:F2} - TotalProfit: {TotalProfit:F2}");

                // LOG DETALHADO: Impacto no SessionProfit
                var sessionProfitBefore = SessionProfit;
                var totalProfitBefore = TotalProfit;

                // Se estiver no modo dual, capturar profits para cálculo do SessionProfit
                // CORREÇÃO CRÍTICA: Verificar se é contrato dual mesmo se não está na lista de pendentes
                // para lidar com contratos que completaram muito rapidamente
                bool isDualContract = IsDualEnabled && (_pendingDualContracts.Contains(contractId) ||
                                     (_isDualEntryPending && _pendingDualContracts.Count < 2));

                if (isDualContract)
                {
                    _logger.LogInformation($"[SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====");
                    _logger.LogInformation($"[SESSION PROFIT] Contract ID: {contractId}");
                    _logger.LogInformation($"[SESSION PROFIT] Profit do contrato: {profit:F2}");
                    _logger.LogInformation($"[SESSION PROFIT] SessionProfit antes: {sessionProfitBefore:F2}");
                    _logger.LogInformation($"[SESSION PROFIT] TotalProfit antes: {totalProfitBefore:F2}");

                    // Remover da lista de pendentes se estiver lá
                    if (_pendingDualContracts.Contains(contractId))
                    {
                        _pendingDualContracts.Remove(contractId);
                    }

                    _logger.LogInformation($"[DUAL DEBUG] Processing dual contract {contractId} with profit {profit:F2}");
                    _logger.LogInformation($"[DUAL DEBUG] Current state - Contract1Completed: {_dualContract1Completed}, Contract2Completed: {_dualContract2Completed}");
                    _logger.LogInformation($"[DUAL DEBUG] Pending contracts: {string.Join(", ", _pendingDualContracts)}");

                    // CORREÇÃO: Determinar qual contrato é este baseado no estado atual
                    int contractIndex;
                    if (!_dualContract1Completed && !_dualContract2Completed)
                    {
                        // Primeiro contrato a completar
                        contractIndex = 0;
                    }
                    else if (_dualContract1Completed && !_dualContract2Completed)
                    {
                        // Segundo contrato a completar
                        contractIndex = 1;
                    }
                    else if (!_dualContract1Completed && _dualContract2Completed)
                    {
                        // Segundo contrato a completar (caso raro)
                        contractIndex = 0;
                    }
                    else
                    {
                        // Ambos já completados - possível duplicação, ignorar
                        _logger.LogWarning($"[DUAL DEBUG] Both contracts already completed - ignoring duplicate for {contractId}");
                        return;
                    }

                    _logger.LogInformation($"[DUAL DEBUG] Contract index: {contractIndex}");

                    if (contractIndex == 0)
                    {
                        _dualContract1Profit = profit;
                        _dualContract1Completed = true;
                        _logger.LogInformation($"[DUAL] Contract 1 finished: Stake={_dualContract1Stake:F2}, Profit={profit:F2}");
                        _logger.LogInformation($"[SESSION PROFIT] Contract 1 - Stake: {_dualContract1Stake:F2}, Profit: {profit:F2}");
                    }
                    else
                    {
                        _dualContract2Profit = profit;
                        _dualContract2Completed = true;
                        _logger.LogInformation($"[DUAL] Contract 2 finished: Stake={_dualContract2Stake:F2}, Profit={profit:F2}");
                        _logger.LogInformation($"[SESSION PROFIT] Contract 2 - Stake: {_dualContract2Stake:F2}, Profit: {profit:F2}");
                    }
                    
                    _logger.LogInformation($"[DUAL DEBUG] After processing - Contract1Completed: {_dualContract1Completed}, Contract2Completed: {_dualContract2Completed}");
                    
                    // Se ambos completaram, chamar ProcessDualLevelComplete imediatamente
                    // para garantir cálculo contínuo da proporção sem pausas
                    if (_dualContract1Completed && _dualContract2Completed)
                    {
                        _logger.LogInformation($"[DUAL DEBUG] ✅ Both contracts completed in OnContractFinished - calling ProcessDualLevelComplete");
                        Console.WriteLine($"[DUAL DEBUG] ✅ Both contracts completed - calling ProcessDualLevelComplete");

                        // Processar imediatamente para evitar pausas no cálculo da proporção
                        // Evitar reentrância: processar conclusão apenas uma vez
                        if (!_isProcessingDualCompletion)
                        {
                            _isProcessingDualCompletion = true;
                            Application.Current.Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    _logger.LogInformation($"[DUAL DEBUG] 🚀 Executing ProcessDualLevelComplete (FAST ASYNC)...");
                                    await ProcessDualLevelCompleteAsync();
                                    _logger.LogInformation($"[DUAL DEBUG] ✅ ProcessDualLevelComplete completed");
                                }
                                finally
                                {
                                    _isProcessingDualCompletion = false;
                                }
                            });
                        }
                    }
                    else
                    {
                        _logger.LogInformation($"[DUAL DEBUG] Waiting for other contract to complete...");
                    }
                }
            }
            else
            {
                // Fallback: se por algum motivo não havia entrada (ex.: reconexão sem evento de compra),
                // adiciona uma linha mínima para que SessionProfit reflita o P/L realizado corretamente.
                _logger.LogWarning($"[FALLBACK] Contract {contractId} finished but no active entry found. Creating minimal entry for accounting.");
                _logger.LogWarning($"[CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract {contractId} não foi encontrado na tabela de profit com IsActive=true!");
                
                // Log todas as entradas para debug
                var allEntries = ProfitTableEntries.Where(e => e.RefId == contractId).ToList();
                _logger.LogWarning($"[CONTRACT_STATUS] Entradas encontradas para {contractId}: {allEntries.Count}");
                foreach (var debugEntry in allEntries)
                {
                    _logger.LogWarning($"[CONTRACT_STATUS] - RefId: {debugEntry.RefId}, IsActive: {debugEntry.IsActive}, Profit: {debugEntry.TotalProfitLoss}");
                }
                // CORREÇÃO: Calcular stake baseado no profit para evitar stakes zeradas
                decimal estimatedStake = 0m;
                if (profit > 0)
                {
                    // Se ganhou, stake = profit / (payout_ratio - 1)
                    // Assumindo payout ratio médio de 1.9
                    estimatedStake = Math.Round(profit / 0.9m, 2);
                }
                else
                {
                    // Se perdeu, stake = -profit (o valor perdido)
                    estimatedStake = Math.Round(-profit, 2);
                }

                // Garantir stake mínima
                if (estimatedStake < 0.35m) estimatedStake = 0.35m;

                var minimal = new ProfitTableEntry
                {
                    RefId = contractId,
                    Contract = GetContractTypeDisplayName(),
                    Duration = GetDurationDisplayText(),
                    EntrySpot = null,
                    ExitSpot = exitTime.ToUniversalTime(),
                    Stake = estimatedStake,
                    Payout = estimatedStake * 1.9m, // Payout estimado
                    EntryPrice = null,
                    ExitPrice = exitPrice,
                    TotalProfitLoss = Math.Round(profit, 2),
                    IsActive = false,
                    SessionId = CurrentDualSession,
                    IsDual = IsDualEnabled
                };
                ProfitTableEntries.Insert(0, minimal);
                Console.WriteLine($"[FALLBACK] Minimal entry added for {contractId} with P/L {minimal.TotalProfitLoss:F2}");

                // CORREÇÃO CRÍTICA: Verificar se é um contrato dual órfão ou perdido
                if (IsDualEnabled)
                {
                    bool wasInPendingList = _pendingDualContracts.Contains(contractId);
                    bool isDualSession = _isDualEntryPending;

                    if (wasInPendingList || isDualSession)
                    {
                        _logger.LogWarning($"[DUAL RECOVERY] Contract result received for dual session - possible fast completion or timeout recovery");
                        _logger.LogWarning($"[DUAL RECOVERY] Contract {contractId} - WasInPending: {wasInPendingList}, IsDualSession: {isDualSession}");

                        if (wasInPendingList)
                        {
                            _pendingDualContracts.Remove(contractId);
                        }

                        // Processar como contrato dual mesmo sem estar na tabela ativa
                        if (!_dualContract1Completed && !_dualContract2Completed)
                        {
                            _dualContract1Profit = profit;
                            _dualContract1Completed = true;
                            _logger.LogWarning($"[DUAL RECOVERY] Assigned as Contract 1 - Profit: {profit:F2}");
                        }
                        else if (_dualContract1Completed && !_dualContract2Completed)
                        {
                            _dualContract2Profit = profit;
                            _dualContract2Completed = true;
                            _logger.LogWarning($"[DUAL RECOVERY] Assigned as Contract 2 - Profit: {profit:F2}");
                        }
                        else
                        {
                            _logger.LogWarning($"[DUAL RECOVERY] Both contracts already completed - ignoring duplicate");
                        }

                        // Se ambos completaram, processar conclusão
                        if (_dualContract1Completed && _dualContract2Completed)
                        {
                            _logger.LogWarning($"[DUAL RECOVERY] Both contracts completed via recovery - calling ProcessDualLevelComplete");

                            if (!_isProcessingDualCompletion)
                            {
                                _isProcessingDualCompletion = true;
                                Application.Current.Dispatcher.InvokeAsync(async () =>
                                {
                                    try
                                    {
                                        await ProcessDualLevelCompleteAsync();
                                    }
                                    finally
                                    {
                                        _isProcessingDualCompletion = false;
                                    }
                                });
                            }
                        }

                        // Se não há mais contratos pendentes, resetar estado dual se necessário
                        if (_pendingDualContracts.Count == 0 && !_dualContract1Completed && !_dualContract2Completed)
                        {
                            _logger.LogWarning($"[DUAL RECOVERY] No more pending contracts and no completed contracts - resetting dual state");
                            _isDualEntryPending = false;
                        }
                    }
                }
            }
        });
    }

    private void OnContractPurchased(string contractId, string contractType, string duration, decimal stake, decimal payout, DateTime purchaseTime)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            _logger.LogInformation($"[CONTRACT PURCHASED EVENT] Contract {contractId} purchased - Type: {contractType}, Stake: {stake:F2}, Payout: {payout:F2}");

            // Registrar contrato pendente para o modo dual
            if (IsDualEnabled && !string.IsNullOrEmpty(contractId))
            {
                _logger.LogInformation($"[CONTRACT PURCHASED EVENT] Adding contract {contractId} to pending dual contracts");
                if (!_pendingDualContracts.Contains(contractId))
                {
                    _pendingDualContracts.Add(contractId);
                }
                _isDualEntryPending = true;
            }

            // Atualizar MaxStake com a stake desta compra
            UpdateMaxStake(stake);

            _logger.LogInformation($"[CONTRACT PURCHASED EVENT] Creating table entry for contract {contractId}");

            var entry = new ProfitTableEntry
            {
                RefId = contractId,
                Contract = contractType,
                Duration = duration,
                // Only use values provided by the API. Do not use local fallbacks.
                EntrySpot = null, // API should populate entry tick_time when available
                ExitSpot = null, // CORREÇÃO: Exit spot deve ficar null até o contrato ser finalizado
                Stake = stake,
                Payout = payout,
                EntryPrice = null, // API should populate entry tick/spot when available; do not use CurrentTickPrice
                ExitPrice = null, // CORREÇÃO: Exit price deve ficar null até o contrato ser finalizado
                IsActive = true,
                SessionId = CurrentDualSession,
                IsDual = IsDualEnabled
            };

            _logger.LogInformation($"[CONTRACT PURCHASED EVENT] Adding entry to table - Contract: {contractId}, Entries before: {ProfitTableEntries.Count}");

            ProfitTableEntries.Insert(0, entry); // Add to top of list

            _logger.LogInformation($"[CONTRACT PURCHASED EVENT] Entry added to table - Entries after: {ProfitTableEntries.Count}");

            // CORREÇÃO: Forçar atualização da UI em tempo real (não precisa de Dispatcher aqui)
            OnPropertyChanged(nameof(ProfitTableEntries));
            OnPropertyChanged(nameof(ActiveExposure));
            OnPropertyChanged(nameof(TotalProfit));
            OnPropertyChanged(nameof(SessionProfit));

            _logger.LogInformation($"[CONTRACT PURCHASED EVENT] UI properties updated for contract {contractId}");

            // Atualizar MaxStake baseado na tabela
            UpdateMaxStakeFromTable();

            // Debug: Verify immediately after adding entry
            Console.WriteLine($"[TABLE DEBUG] Manual entry added - RefId: {entry.RefId}, P/L: {entry.TotalProfitLoss:F2}");
            VerifyTotalProfitCalculation();

            // Keep only last 50 entries to avoid memory issues
            while (ProfitTableEntries.Count > 50)
            {
                ProfitTableEntries.RemoveAt(ProfitTableEntries.Count - 1);
            }

            _logger.LogInformation($"Profit Table entry added for automatic purchase - Contract: {contractId}, Type: {contractType}, PurchaseTime: {purchaseTime:HH:mm:ss}");
        });
    }

    #region Configuration Management

    /// <summary>
    /// Carrega a configuração salva e aplica aos controles da interface
    /// </summary>
    private async Task LoadConfigurationAsync()
    {
        try
        {
            _logger.LogInformation("[CONFIG] Carregando configuração do usuário...");
            
            var config = await _configService.LoadConfigurationAsync();
            
            // Aplicar configurações básicas
            Stake = config.Stake;
            // Refletir imediatamente no campo de texto
            StakeAmount = config.Stake.ToString("F2");
            DualTakeProfit = config.DualTakeProfit;
            DualLevel = config.DualLevel;
            DualSession = config.DualSession;

            // Aplicar configurações do novo modo Dual
            DualLucroAlvo = config.DualLucroAlvo;
            DualAlfa = config.DualAlfa;
            DualLucroBase = config.DualLucroBase;
            DualP = config.DualP;
            DualMaxLossAmount = config.DualMaxLossAmount;

            DurationValue = config.DurationValue;
            DurationUnit = config.DurationUnit;
            
            // Aplicar configurações de Money Management - Martingale
            MartingaleFactor = config.MartingaleFactor;
            InitialStakeAmount = config.InitialStakeAmount;
            MartingaleLevel = config.MartingaleLevel;
            MaxLossAmount = config.MaxLossAmount;
            
            // Aplicar estratégias
            IsMartingaleEnabled = config.IsMartingaleEnabled;
            IsFastMartingale = config.IsFastMartingale;
            
            // Aplicar modo dual
            IsDualEnabled = config.IsDualEnabled;
            IsDualAutoMode = config.IsDualAutoMode;

            
            // Seleções serão aplicadas quando os dados estiverem disponíveis
            // (após conectar com a API)
            _pendingMarketSelection = config.SelectedMarket;
            _pendingSubMarketSelection = config.SelectedSubMarket;
            _pendingActiveSymbolSelection = config.SelectedActiveSymbol;
            _pendingContractTypeSelection = config.SelectedContractType;
            _pendingDualContractTypeSelection = config.SelectedDualContractType;
            
            _logger.LogInformation($"[CONFIG] Configuração carregada - Stake: {config.Stake:F2}, DualTakeProfit: {config.DualTakeProfit:F2}");
            _logger.LogInformation($"[CONFIG] Money Management - MartingaleFactor: {config.MartingaleFactor:F2}, InitialStake: {config.InitialStakeAmount:F2}, MaxLevel: {config.MartingaleLevel}, MaxLoss: {config.MaxLossAmount:F2}");
            _logger.LogInformation($"[CONFIG] Estratégias - Martingale: {config.IsMartingaleEnabled}, Dual: {config.IsDualEnabled}");

            // CORREÇÃO: Inicializar MaxStake após carregar configuração
            InitializeMaxStake();

            Console.WriteLine($"[CONFIG] Configuração carregada com sucesso!");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[CONFIG] Erro ao carregar configuração");
            Console.WriteLine($"[CONFIG ERROR] {ex.Message}");
        }
    }

    /// <summary>
    /// Salva a configuração atual do usuário
    /// </summary>
    private readonly object _configSaveLock = new();
    private CancellationTokenSource? _configSaveCts;
    private async Task SaveConfigurationCoreAsync()
    {
        _logger.LogInformation("[CONFIG] Salvando configuração do usuário...");

        // Carregar existente para evitar sobrescrever seleções com null
        var existing = await _configService.LoadConfigurationAsync();

        // Escolher melhor valor disponível (seleção atual -> pendente -> existente)
        var selMarket = SelectedMarket ?? _pendingMarketSelection ?? existing.SelectedMarket;
        var selSubMarket = SelectedSubMarket ?? _pendingSubMarketSelection ?? existing.SelectedSubMarket;
        var selActive = SelectedActiveSymbol?.Symbol ?? _pendingActiveSymbolSelection ?? existing.SelectedActiveSymbol;
        var selContract = SelectedContractType?.ContractType ?? _pendingContractTypeSelection ?? existing.SelectedContractType;
        var selDualContract = SelectedDualContractType?.ContractType ?? _pendingDualContractTypeSelection ?? existing.SelectedDualContractType;

        var config = new UserConfiguration
        {
            Stake = Stake,
            DualTakeProfit = DualTakeProfit,
            DualLevel = DualLevel,
            DualSession = DualSession,
            DualLucroAlvo = DualLucroAlvo,
            DualAlfa = DualAlfa,
            DualLucroBase = DualLucroBase,
            DualP = DualP,
            DualMaxLossAmount = DualMaxLossAmount,
            MartingaleFactor = MartingaleFactor,
            InitialStakeAmount = InitialStakeAmount,
            MartingaleLevel = MartingaleLevel,
            MaxLossAmount = MaxLossAmount,
            DurationValue = DurationValue,
            DurationUnit = DurationUnit,
            SelectedMarket = selMarket,
            SelectedSubMarket = selSubMarket,
            SelectedActiveSymbol = selActive,
            SelectedContractType = selContract,
            SelectedDualContractType = selDualContract,
            IsMartingaleEnabled = IsMartingaleEnabled,
            IsDualEnabled = IsDualEnabled,
            IsDualAutoMode = IsDualAutoMode,
            IsFastMartingale = IsFastMartingale,

        };

        await _configService.SaveConfigurationAsync(config);
        _logger.LogInformation("[CONFIG] Configuração salva com sucesso");
        Console.WriteLine("[CONFIG] Configuração salva!");
    }

    // Debounce de ~300ms para evitar salvar repetidamente e bloquear o arquivo
    public Task SaveConfigurationAsync()
    {
        lock (_configSaveLock)
        {
            _configSaveCts?.Cancel();
            _configSaveCts = new CancellationTokenSource();
            var token = _configSaveCts.Token;
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(300, token);
                    await SaveConfigurationCoreAsync();
                }
                catch (TaskCanceledException) { }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[CONFIG] Erro ao salvar configuração");
                }
            }, token);
        }
        return Task.CompletedTask;
    }

    /// <summary>
    /// Campos para armazenar seleções pendentes até que os dados estejam disponíveis
    /// </summary>
    private string? _pendingMarketSelection;
    private string? _pendingSubMarketSelection;
    private string? _pendingActiveSymbolSelection;
    private string? _pendingContractTypeSelection;
    private string? _pendingDualContractTypeSelection;

    /// <summary>
    /// Aplica as seleções pendentes quando os dados ficam disponíveis
    /// </summary>
    private void ApplyPendingSelections()
    {
        try
        {
            // Aplicar seleção de mercado
            if (!string.IsNullOrEmpty(_pendingMarketSelection) && Markets?.Any() == true)
            {
                var market = Markets.FirstOrDefault(m => string.Equals(m, _pendingMarketSelection, StringComparison.OrdinalIgnoreCase));
                if (market != null)
                {
                    SelectedMarket = market;
                    _logger.LogInformation($"[CONFIG] Mercado restaurado: {market}");
                }
            }
            
            // Aplicar seleção de submercado
            if (!string.IsNullOrEmpty(_pendingSubMarketSelection) && SubMarkets?.Any() == true)
            {
                var subMarket = SubMarkets.FirstOrDefault(sm => string.Equals(sm, _pendingSubMarketSelection, StringComparison.OrdinalIgnoreCase));
                if (subMarket != null)
                {
                    SelectedSubMarket = subMarket;
                    _logger.LogInformation($"[CONFIG] Submercado restaurado: {subMarket}");
                }
            }
            
            // Aplicar seleção de ativo
            if (!string.IsNullOrEmpty(_pendingActiveSymbolSelection) && ActiveSymbols?.Any() == true)
            {
                var symbol = ActiveSymbols.FirstOrDefault(s => string.Equals(s.Symbol, _pendingActiveSymbolSelection, StringComparison.OrdinalIgnoreCase));
                if (symbol != null)
                {
                    SelectedActiveSymbol = symbol;
                    _logger.LogInformation($"[CONFIG] Ativo restaurado: {symbol.Symbol}");
                }
            }
            
            // Aplicar seleção de tipo de contrato
            if (!string.IsNullOrEmpty(_pendingContractTypeSelection) && ContractTypes?.Any() == true)
            {
                var contractType = ContractTypes.FirstOrDefault(c => string.Equals(c.ContractType, _pendingContractTypeSelection, StringComparison.OrdinalIgnoreCase));
                if (contractType != null)
                {
                    SelectedContractType = contractType;
                    _logger.LogInformation($"[CONFIG] Tipo de contrato restaurado: {contractType.ContractType}");
                }
            }

            // Aplicar seleção de tipo de contrato dual
            if (!string.IsNullOrEmpty(_pendingDualContractTypeSelection) && DualContractTypes?.Any() == true)
            {
                var dualType = DualContractTypes.FirstOrDefault(c => string.Equals(c.ContractType, _pendingDualContractTypeSelection, StringComparison.OrdinalIgnoreCase));
                if (dualType != null)
                {
                    SelectedDualContractType = dualType;
                    _logger.LogInformation($"[CONFIG] Tipo de contrato dual restaurado: {dualType.ContractType}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[CONFIG] Erro ao aplicar seleções pendentes");
        }
    }

    /// <summary>
    /// Inicializa o timer para verificações periódicas de pausa automática
    /// </summary>
    private void InitializeAutoPauseTimer()
    {
        try
        {
            // Timer de pausa automática removido
            // _autoPauseTimer = new System.Threading.Timer(
            //     callback: _ => CheckAutoPauseConditions(),
            //     state: null,
            //     dueTime: TimeSpan.FromSeconds(30),
            //     period: TimeSpan.FromSeconds(30)
            // );
            
            _logger.LogInformation("[AUTO-PAUSE] Timer de pausa automática inicializado com intervalo de 30 segundos");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[AUTO-PAUSE] Erro ao inicializar timer de pausa automática");
        }
    }

    /// <summary>
    /// Para o timer de pausa automática
    /// </summary>
    private void StopAutoPauseTimer()
    {
        try
        {
            // Timer de pausa automática removido
            // _autoPauseTimer?.Dispose();
            // _autoPauseTimer = null;
            _logger.LogInformation("[AUTO-PAUSE] Timer de pausa automática removido");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[AUTO-PAUSE] Erro relacionado ao timer removido");
        }
    }

    /// <summary>
    /// Comando para salvar configuração manualmente
    /// </summary>
    public ICommand SaveConfigCommand => new RelayCommand(async () => await SaveConfigurationAsync());

    /// <summary>
    /// Comando para carregar configuração manualmente
    /// </summary>
    public ICommand LoadConfigCommand => new RelayCommand(async () => await LoadConfigurationAsync());

    /// <summary>
    /// Implementação do IDisposable para limpeza de recursos
    /// </summary>
    public void Dispose()
    {
        try
        {
            StopAutoPauseTimer();
            _logger.LogInformation("[MAIN-VIEW-MODEL] Recursos limpos com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[MAIN-VIEW-MODEL] Erro durante limpeza de recursos");
        }
    }

    #endregion
}
